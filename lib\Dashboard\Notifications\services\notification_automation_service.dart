import 'dart:async';
import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

import '../../../Dashboard/Health/models/health_record_isar.dart';
import '../../../Dashboard/Breeding/models/breeding_record_isar.dart';
import '../../../Dashboard/Weight/models/weight_record_isar.dart';
import '../../../Dashboard/Milk Records/models/milk_record_isar.dart';
import '../../../Dashboard/Cattle/models/cattle_isar.dart';
import '../../../Dashboard/Cattle/services/cattle_repository.dart';
import '../../../Dashboard/Health/services/health_repository.dart';

import '../../../Dashboard/Weight/services/weight_repository.dart';
import '../../../Dashboard/Milk Records/services/milk_repository.dart';

import 'notification_repository.dart';
import 'notification_settings_repository.dart';
import '../models/notification_request.dart';
import '../models/notification_priority.dart';
import '../models/scheduled_notification_request.dart';
import '../models/notification_isar.dart';

/// Service for automating notification generation based on events and data patterns
class NotificationAutomationService {
  final Logger _logger = Logger('NotificationAutomationService');
  final NotificationRepository _notificationRepository;
  final NotificationSettingsRepository _settingsRepository;
  final CattleRepository _cattleRepository;
  final HealthRepository _healthRepository;
  final WeightRepository _weightRepository;
  final MilkRepository _milkRepository;
  final Uuid _uuid = const Uuid();
  
  /// Timer for scheduled tasks
  Timer? _scheduledTasksTimer;
  
  /// Constructor with dependencies
  NotificationAutomationService(
    this._notificationRepository,
    this._settingsRepository,
    this._cattleRepository,
    this._healthRepository,
    this._weightRepository,
    this._milkRepository,
  );
  
  /// Initialize the service
  Future<void> initialize() async {
    try {
      // Start scheduled tasks timer (runs every hour)
      _scheduledTasksTimer = Timer.periodic(
        const Duration(hours: 1),
        (_) => _runScheduledTasks(),
      );
      
      _logger.info('NotificationAutomationService initialized');
    } catch (e) {
      _logger.severe('Error initializing NotificationAutomationService: $e');
    }
  }
  
  /// Run scheduled tasks
  Future<void> _runScheduledTasks() async {
    try {
      // Check for overdue events
      await checkOverdueEvents();
      
      // Analyze health trends for all cattle
      await analyzeAllCattleHealthTrends();
      
      // Analyze milk production trends for all cattle
      await analyzeAllCattleMilkProductionTrends();
      
      // Check for cattle age milestones
      await checkCattleAgeMilestones();
      
      _logger.info('Scheduled tasks completed');
    } catch (e) {
      _logger.severe('Error running scheduled tasks: $e');
    }
  }  

  /// Handle health record creation
  Future<void> onHealthRecordCreated(HealthRecordIsar record) async {
    try {
      // Get cattle details
      final cattle = await _cattleRepository.getCattleByBusinessId(record.cattleBusinessId ?? '');
      if (cattle == null) {
        _logger.warning('Cattle not found for health record: ${record.businessId}');
        return;
      }
      
      // Create notification for the health record
      await _createNotification(
        title: 'Health Record Added',
        message: 'A new health record has been added for ${cattle.name}: ${record.diagnosis ?? 'No diagnosis'}',
        category: 'health',
        type: 'info',
        priority: NotificationPriority.medium,
        cattleId: cattle.businessId,
        relatedRecordId: record.businessId,
        relatedRecordType: 'health_record',
      );
      
      // Schedule follow-up reminders if needed
      if (record.followUpDate != null) {
        // Get lead time from settings
        final settings = await _settingsRepository.getSettings();
        final leadTimes = settings.getReminderLeadTimes();
        final leadTimeHours = leadTimes['health'] ?? 24;
        
        // Schedule reminder before follow-up date
        final reminderDate = record.followUpDate!.subtract(Duration(hours: leadTimeHours));
        
        await scheduleNotification(
          ScheduledNotificationRequest(
            title: 'Health Follow-Up Reminder',
            message: 'Follow-up for ${cattle.name} is due soon: ${record.diagnosis ?? 'No diagnosis'}',
            category: 'health',
            type: 'reminder',
            priority: NotificationPriority.high,
            cattleId: cattle.businessId,
            relatedRecordId: record.businessId,
            relatedRecordType: 'health_record',
            scheduledFor: reminderDate,
          ),
        );
        
        // Schedule notification on the follow-up date
        await scheduleNotification(
          ScheduledNotificationRequest(
            title: 'Health Follow-Up Due',
            message: 'Follow-up for ${cattle.name} is due today: ${record.diagnosis ?? 'No diagnosis'}',
            category: 'health',
            type: 'alert',
            priority: NotificationPriority.high,
            cattleId: cattle.businessId,
            relatedRecordId: record.businessId,
            relatedRecordType: 'health_record',
            scheduledFor: record.followUpDate ?? DateTime.now().add(const Duration(days: 7)),
          ),
        );
      }
      
      _logger.info('Processed health record creation: ${record.businessId}');
    } catch (e) {
      _logger.severe('Error processing health record creation: $e');
    }
  }
  
  /// Handle breeding event occurrence
  Future<void> onBreedingEventOccurred(BreedingRecordIsar record) async {
    try {
      // Get cattle details
      final cattle = await _cattleRepository.getCattleByBusinessId(record.cattleBusinessId ?? '');
      if (cattle == null) {
        _logger.warning('Cattle not found for breeding record: ${record.businessId}');
        return;
      }
      
      // Create notification for the breeding event
      await _createNotification(
        title: 'Breeding Event Recorded',
        message: 'A breeding event has been recorded for ${cattle.name}',
        category: 'breeding',
        type: 'info',
        priority: NotificationPriority.medium,
        cattleId: cattle.businessId,
        relatedRecordId: record.businessId,
        relatedRecordType: 'breeding_record',
      );
      
      // Schedule pregnancy check reminder (typically 30-45 days after breeding)
      if (record.date != null) {
        final pregnancyCheckDate = record.date!.add(const Duration(days: 30));
        
        // Get lead time from settings
        final settings = await _settingsRepository.getSettings();
        final leadTimes = settings.getReminderLeadTimes();
        final leadTimeHours = leadTimes['breeding'] ?? 12;
        
        // Schedule reminder before pregnancy check date
        final reminderDate = pregnancyCheckDate.subtract(Duration(hours: leadTimeHours));
        
        await scheduleNotification(
          ScheduledNotificationRequest(
            title: 'Pregnancy Check Reminder',
            message: 'Schedule a pregnancy check for ${cattle.name}',
            category: 'breeding',
            type: 'reminder',
            priority: NotificationPriority.high,
            cattleId: cattle.businessId,
            relatedRecordId: record.businessId,
            relatedRecordType: 'breeding_record',
            scheduledFor: reminderDate,
          ),
        );
        
        // Schedule notification on the pregnancy check date
        await scheduleNotification(
          ScheduledNotificationRequest(
            title: 'Pregnancy Check Due',
            message: 'Pregnancy check for ${cattle.name} is due today',
            category: 'breeding',
            type: 'alert',
            priority: NotificationPriority.high,
            cattleId: cattle.businessId,
            relatedRecordId: record.businessId,
            relatedRecordType: 'breeding_record',
            scheduledFor: pregnancyCheckDate,
          ),
        );
      }
      
      _logger.info('Processed breeding event: ${record.businessId}');
    } catch (e) {
      _logger.severe('Error processing breeding event: $e');
    }
  }
  
  /// Handle weight measurement
  Future<void> onWeightMeasured(WeightRecordIsar record) async {
    try {
      // Get cattle details
      final cattle = await _cattleRepository.getCattleByBusinessId(record.cattleBusinessId ?? '');
      if (cattle == null) {
        _logger.warning('Cattle not found for weight record: ${record.businessId}');
        return;
      }
      
      // Create notification for the weight record
      await _createNotification(
        title: 'Weight Recorded',
        message: 'Weight recorded for ${cattle.name}: ${record.weight} kg',
        category: 'weight',
        type: 'info',
        priority: NotificationPriority.medium,
        cattleId: cattle.businessId,
        relatedRecordId: record.businessId,
        relatedRecordType: 'weight_record',
      );
      
      // Analyze weight trend for this cattle
      await analyzeWeightTrends(cattle.businessId ?? '');
      
      _logger.info('Processed weight measurement: ${record.businessId}');
    } catch (e) {
      _logger.severe('Error processing weight measurement: $e');
    }
  }
  
  /// Handle milk recording
  Future<void> onMilkRecorded(MilkRecordIsar record) async {
    try {
      // Get cattle details
      final cattle = await _cattleRepository.getCattleByBusinessId(record.cattleBusinessId ?? '');
      if (cattle == null) {
        _logger.warning('Cattle not found for milk record: ${record.businessId}');
        return;
      }
      
      // Create notification for the milk record
      await _createNotification(
        title: 'Milk Production Recorded',
        message: 'Milk recorded for ${cattle.name}: ${record.quantity} liters',
        category: 'milk',
        type: 'info',
        priority: NotificationPriority.medium,
        cattleId: cattle.businessId,
        relatedRecordId: record.businessId,
        relatedRecordType: 'milk_record',
      );
      
      // Analyze milk production trend for this cattle
      await analyzeMilkProductionTrends(cattle.businessId ?? '');
      
      _logger.info('Processed milk recording: ${record.businessId}');
    } catch (e) {
      _logger.severe('Error processing milk recording: $e');
    }
  }

  /// Analyze health trends for a specific cattle
  Future<void> analyzeHealthTrends(String cattleId) async {
    try {
      // Get cattle details
      final cattle = await _cattleRepository.getCattleByBusinessId(cattleId);
      if (cattle == null) {
        _logger.warning('Cattle not found for health trend analysis: $cattleId');
        return;
      }
      
      // Get recent health records
      final healthRecords = await _healthRepository.getHealthRecordsByCattleId(cattleId);
      
      // Skip if not enough records
      if (healthRecords.length < 2) {
        return;
      }
      
      // Sort by date
      healthRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      
      // Check for recurring health issues
      final recentDiagnoses = <String>{};
      for (final record in healthRecords.take(3)) {
        if (record.diagnosis != null && record.diagnosis!.isNotEmpty) {
          recentDiagnoses.add(record.diagnosis!.toLowerCase());
        }
      }
      
      // Check for recurring issues
      if (recentDiagnoses.length < 3 && healthRecords.length >= 3) {
        // If the same diagnosis appears multiple times in recent records
        await _createNotification(
          title: 'Recurring Health Issue Detected',
          message: '${cattle.name} has had recurring health issues: ${recentDiagnoses.join(", ")}',
          category: 'health',
          type: 'warning',
          priority: NotificationPriority.high,
          cattleId: cattle.businessId,
          relatedRecordType: 'health_trend',
        );
      }
      
      _logger.info('Analyzed health trends for cattle: $cattleId');
    } catch (e) {
      _logger.severe('Error analyzing health trends: $e');
    }
  }
  
  /// Analyze health trends for all cattle
  Future<void> analyzeAllCattleHealthTrends() async {
    try {
      // Get all cattle
      final allCattle = await _cattleRepository.getAllCattle();
      
      // Analyze trends for each cattle
      for (final cattle in allCattle) {
        if (cattle.businessId != null) {
          await analyzeHealthTrends(cattle.businessId!);
        }
      }
      
      _logger.info('Analyzed health trends for all cattle');
    } catch (e) {
      _logger.severe('Error analyzing health trends for all cattle: $e');
    }
  }
  
  /// Analyze weight trends for a specific cattle
  Future<void> analyzeWeightTrends(String cattleId) async {
    try {
      // Get cattle details
      final cattle = await _cattleRepository.getCattleByBusinessId(cattleId);
      if (cattle == null) {
        _logger.warning('Cattle not found for weight trend analysis: $cattleId');
        return;
      }
      
      // Get recent weight records
      final weightRecords = await _weightRepository.getWeightRecordsByCattleId(cattleId);
      
      // Skip if not enough records
      if (weightRecords.length < 3) {
        return;
      }
      
      // Sort by date
      weightRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      
      // Check for weight loss trend
      bool weightLossTrend = true;
      for (int i = 0; i < weightRecords.length - 1 && i < 2; i++) {
        final currentWeight = weightRecords[i].weight;
        final previousWeight = weightRecords[i + 1].weight;
        
        if (currentWeight >= previousWeight) {
          weightLossTrend = false;
          break;
        }
      }
      
      if (weightLossTrend) {
        final latestWeight = weightRecords.first.weight;
        final oldestWeight = weightRecords[2].weight;
        final weightLoss = oldestWeight - latestWeight;
        final percentLoss = (weightLoss / oldestWeight) * 100;
        
        if (percentLoss > 5) {
          await _createNotification(
            title: 'Weight Loss Alert',
            message: '${cattle.name} has lost ${percentLoss.toStringAsFixed(1)}% of weight in the last 3 measurements',
            category: 'weight',
            type: 'warning',
            priority: NotificationPriority.high,
            cattleId: cattle.businessId,
            relatedRecordType: 'weight_trend',
          );
        }
      }
      
      _logger.info('Analyzed weight trends for cattle: $cattleId');
    } catch (e) {
      _logger.severe('Error analyzing weight trends: $e');
    }
  }
  
  /// Analyze milk production trends for a specific cattle
  Future<void> analyzeMilkProductionTrends(String cattleId) async {
    try {
      // Get cattle details
      final cattle = await _cattleRepository.getCattleByBusinessId(cattleId);
      if (cattle == null) {
        _logger.warning('Cattle not found for milk trend analysis: $cattleId');
        return;
      }
      
      // Get recent milk records
      final milkRecords = await _milkRepository.getMilkRecordsByCattleId(cattleId);
      
      // Skip if not enough records
      if (milkRecords.length < 5) {
        return;
      }
      
      // Sort by date
      milkRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      
      // Calculate average of last 5 records
      double recentAverage = 0;
      for (int i = 0; i < 5; i++) {
        recentAverage += milkRecords[i].quantity ?? 0;
      }
      recentAverage /= 5;
      
      // Calculate average of previous 5 records (if available)
      if (milkRecords.length >= 10) {
        double previousAverage = 0;
        for (int i = 5; i < 10; i++) {
          previousAverage += milkRecords[i].quantity ?? 0;
        }
        previousAverage /= 5;
        
        // Check for significant drop in production
        if (recentAverage < previousAverage * 0.85) {
          final percentDrop = ((previousAverage - recentAverage) / previousAverage) * 100;
          
          await _createNotification(
            title: 'Milk Production Drop',
            message: '${cattle.name} has had a ${percentDrop.toStringAsFixed(1)}% drop in milk production',
            category: 'milk',
            type: 'warning',
            priority: NotificationPriority.high,
            cattleId: cattle.businessId,
            relatedRecordType: 'milk_trend',
          );
        }
      }
      
      _logger.info('Analyzed milk production trends for cattle: $cattleId');
    } catch (e) {
      _logger.severe('Error analyzing milk production trends: $e');
    }
  }
  
  /// Analyze milk production trends for all cattle
  Future<void> analyzeAllCattleMilkProductionTrends() async {
    try {
      // Get all cattle
      final allCattle = await _cattleRepository.getAllCattle();
      
      // Analyze trends for each cattle
      for (final cattle in allCattle) {
        if (cattle.businessId != null) {
          await analyzeMilkProductionTrends(cattle.businessId!);
        }
      }
      
      _logger.info('Analyzed milk production trends for all cattle');
    } catch (e) {
      _logger.severe('Error analyzing milk production trends for all cattle: $e');
    }
  }

  /// Check for overdue events
  Future<void> checkOverdueEvents() async {
    try {
      // Get overdue notifications
      final overdueNotifications = await _notificationRepository.getOverdueNotifications();
      
      // Group by cattle
      final cattleNotifications = <String, List<String>>{};
      
      for (final notification in overdueNotifications) {
        if (notification.cattleId != null) {
          cattleNotifications.putIfAbsent(notification.cattleId!, () => []);
          if (notification.title != null) {
            cattleNotifications[notification.cattleId!]!.add(notification.title!);
          }
        }
      }
      
      // Create escalation notifications for each cattle with overdue items
      for (final entry in cattleNotifications.entries) {
        final cattleId = entry.key;
        final items = entry.value;
        
        if (items.isEmpty) continue;
        
        // Get cattle details
        final cattle = await _cattleRepository.getCattleByBusinessId(cattleId);
        if (cattle == null) continue;
        
        // Create escalation notification
        await _createNotification(
          title: 'Overdue Items for ${cattle.name}',
          message: '${items.length} overdue items: ${items.take(3).join(", ")}${items.length > 3 ? "..." : ""}',
          category: 'events',
          type: 'alert',
          priority: NotificationPriority.high,
          cattleId: cattle.businessId,
          relatedRecordType: 'overdue_events',
        );
      }
      
      _logger.info('Checked for overdue events');
    } catch (e) {
      _logger.severe('Error checking for overdue events: $e');
    }
  }
  
  /// Check for cattle age milestones
  Future<void> checkCattleAgeMilestones() async {
    try {
      // Get all cattle
      final allCattle = await _cattleRepository.getAllCattle();
      
      final now = DateTime.now();
      
      for (final cattle in allCattle) {
        if (cattle.dateOfBirth == null || cattle.businessId == null) continue;
        
        final ageInDays = now.difference(cattle.dateOfBirth!).inDays;
        
        // Check for various age milestones
        
        // Weaning age (around 6-8 months)
        if (ageInDays >= 180 && ageInDays <= 185) {
          await _createNotification(
            title: 'Weaning Age Milestone',
            message: '${cattle.name} has reached weaning age (6 months)',
            category: 'events',
            type: 'info',
            priority: NotificationPriority.medium,
            cattleId: cattle.businessId,
            relatedRecordType: 'age_milestone',
          );
        }
        
        // Breeding readiness for heifers (around 15 months)
        if (ageInDays >= 450 && ageInDays <= 455 && cattle.gender == CattleGender.female) {
          await _createNotification(
            title: 'Breeding Readiness',
            message: '${cattle.name} has reached breeding age (15 months)',
            category: 'breeding',
            type: 'info',
            priority: NotificationPriority.high,
            cattleId: cattle.businessId,
            relatedRecordType: 'age_milestone',
          );
        }
        
        // First calving (around 24 months)
        if (ageInDays >= 720 && ageInDays <= 725 && cattle.gender == CattleGender.female) {
          await _createNotification(
            title: 'First Calving Age',
            message: '${cattle.name} has reached typical first calving age (24 months)',
            category: 'breeding',
            type: 'info',
            priority: NotificationPriority.medium,
            cattleId: cattle.businessId,
            relatedRecordType: 'age_milestone',
          );
        }
      }
      
      _logger.info('Checked cattle age milestones');
    } catch (e) {
      _logger.severe('Error checking cattle age milestones: $e');
    }
  }
  
  /// Schedule a notification for future delivery
  Future<void> scheduleNotification(ScheduledNotificationRequest request) async {
    try {
      // Create notification with scheduled date
      final notification = NotificationIsar(
        title: request.title,
        message: request.message,
        category: request.category,
        type: request.type,
        priority: request.priority,
        cattleId: request.cattleId,
        relatedRecordId: request.relatedRecordId,
        relatedRecordType: request.relatedRecordType,
        eventId: request.eventId,
        imageUrl: request.imageUrl,
        actionUrl: request.actionUrl,
        customData: request.customData,
        scheduledFor: request.scheduledFor,
        expiresAt: request.expiresAt,
        isRecurring: request.isRecurring,
        recurringPattern: request.recurringPattern,
        createdAt: DateTime.now(),
        businessId: _uuid.v4(),
      );
      
      // Save to repository
      await _notificationRepository.saveNotification(notification);
      
      _logger.info('Scheduled notification for ${request.scheduledFor}');
    } catch (e) {
      _logger.severe('Error scheduling notification: $e');
      rethrow;
    }
  }
  
  /// Cancel a scheduled notification
  Future<void> cancelScheduledNotification(String notificationId) async {
    try {
      await _notificationRepository.deleteNotification(notificationId);
      _logger.info('Cancelled scheduled notification: $notificationId');
    } catch (e) {
      _logger.severe('Error cancelling scheduled notification: $e');
      rethrow;
    }
  }
  
  /// Helper method to create a notification
  Future<void> _createNotification({
    required String title,
    required String message,
    required String category,
    required String type,
    required NotificationPriority priority,
    String? cattleId,
    String? relatedRecordId,
    String? relatedRecordType,
    String? eventId,
    String? imageUrl,
    String? actionUrl,
    Map<String, String>? customData,
  }) async {
    try {
      // Create notification request
      final request = NotificationRequest(
        title: title,
        message: message,
        category: category,
        type: type,
        priority: priority,
        cattleId: cattleId,
        relatedRecordId: relatedRecordId,
        relatedRecordType: relatedRecordType,
        eventId: eventId,
        imageUrl: imageUrl,
        actionUrl: actionUrl,
        customData: customData,
      );
      
      // Create notification
      final notification = NotificationIsar(
        title: request.title,
        message: request.message,
        category: request.category,
        type: request.type,
        priority: request.priority,
        cattleId: request.cattleId,
        relatedRecordId: request.relatedRecordId,
        relatedRecordType: request.relatedRecordType,
        eventId: request.eventId,
        imageUrl: request.imageUrl,
        actionUrl: request.actionUrl,
        customData: request.customData,
        createdAt: DateTime.now(),
        businessId: _uuid.v4(),
      );
      
      // Save to repository
      await _notificationRepository.saveNotification(notification);
    } catch (e) {
      _logger.severe('Error creating notification: $e');
    }
  }
  
  /// Dispose resources
  void dispose() {
    _scheduledTasksTimer?.cancel();
  }
}