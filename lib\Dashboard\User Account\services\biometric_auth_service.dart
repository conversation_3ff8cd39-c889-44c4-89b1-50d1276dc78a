import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'auth_service.dart';
import 'user_repository.dart';
import '../models/user_isar.dart';

/// Service for handling biometric authentication (fingerprint, face ID, etc.)
class BiometricAuthService {
  static final BiometricAuthService _instance = BiometricAuthService._internal();
  factory BiometricAuthService() => _instance;
  BiometricAuthService._internal();

  final Logger _logger = Logger('BiometricAuthService');
  final LocalAuthentication _localAuth = LocalAuthentication();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  // Storage keys for biometric authentication
  static const String _biometricEnabledKey = 'biometric_auth_enabled';
  static const String _biometricUserIdKey = 'biometric_user_id';

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.isDeviceSupported();
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      
      _logger.info('Device biometric support: $isAvailable, Can check: $canCheckBiometrics');
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      _logger.warning('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types on the device
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final List<BiometricType> availableBiometrics = await _localAuth.getAvailableBiometrics();
      _logger.info('Available biometrics: $availableBiometrics');
      return availableBiometrics;
    } catch (e) {
      _logger.warning('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Check if biometric authentication is enabled for the current user
  Future<bool> isBiometricEnabled() async {
    try {
      final String? enabled = await _secureStorage.read(key: _biometricEnabledKey);
      return enabled == 'true';
    } catch (e) {
      _logger.warning('Error checking if biometric is enabled: $e');
      return false;
    }
  }

  /// Enable biometric authentication for the current user
  Future<BiometricAuthResult> enableBiometricAuth(UserIsar user) async {
    try {
      // Check if biometric is available
      if (!await isBiometricAvailable()) {
        return BiometricAuthResult.failure('Biometric authentication is not available on this device');
      }

      // Check if there are any biometrics enrolled
      final availableBiometrics = await getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return BiometricAuthResult.failure('No biometric authentication methods are set up on this device');
      }

      // Authenticate to confirm user identity before enabling
      final bool authenticated = await _authenticateWithBiometric(
        reason: 'Authenticate to enable biometric login for your account',
      );

      if (!authenticated) {
        return BiometricAuthResult.failure('Biometric authentication failed');
      }

      // Store biometric settings
      await _secureStorage.write(key: _biometricEnabledKey, value: 'true');
      await _secureStorage.write(key: _biometricUserIdKey, value: user.businessId);

      _logger.info('Biometric authentication enabled for user: $user.email');
      return BiometricAuthResult.success('Biometric authentication enabled successfully');
    } catch (e) {
      _logger.severe('Error enabling biometric auth: $e');
      return BiometricAuthResult.failure('Failed to enable biometric authentication: ${e.toString()}');
    }
  }

  /// Disable biometric authentication
  Future<BiometricAuthResult> disableBiometricAuth() async {
    try {
      await _secureStorage.delete(key: _biometricEnabledKey);
      await _secureStorage.delete(key: _biometricUserIdKey);

      _logger.info('Biometric authentication disabled');
      return BiometricAuthResult.success('Biometric authentication disabled');
    } catch (e) {
      _logger.warning('Error disabling biometric auth: $e');
      return BiometricAuthResult.failure('Failed to disable biometric authentication');
    }
  }

  /// Authenticate user with biometric and log them in
  Future<BiometricAuthResult> authenticateAndLogin() async {
    try {
      // Check if biometric is enabled
      if (!await isBiometricEnabled()) {
        return BiometricAuthResult.failure('Biometric authentication is not enabled');
      }

      // Check if biometric is available
      if (!await isBiometricAvailable()) {
        return BiometricAuthResult.failure('Biometric authentication is not available');
      }

      // Get the stored user ID
      final String? userId = await _secureStorage.read(key: _biometricUserIdKey);
      if (userId == null) {
        return BiometricAuthResult.failure('No user associated with biometric authentication');
      }

      // Perform biometric authentication
      final bool authenticated = await _authenticateWithBiometric(
        reason: 'Use your fingerprint to sign in to Cattle Manager',
      );

      if (!authenticated) {
        return BiometricAuthResult.failure('Biometric authentication failed');
      }

      // Get the auth service and find the user
      final authService = GetIt.instance<AuthService>();
      // Access user repository directly since it's not exposed in AuthService
      final userRepository = UserRepository();
      final user = await userRepository.getUserByBusinessId(userId);

      if (user == null) {
        return BiometricAuthResult.failure('User not found');
      }

      // Log in the user using the auth service
      final loginResult = await authService.loginWithGoogleUser(user, rememberMe: true);
      
      if (loginResult.success) {
        _logger.info('Biometric login successful for user: $user.email');
        return BiometricAuthResult.success('Biometric login successful');
      } else {
        return BiometricAuthResult.failure(loginResult.message);
      }
    } catch (e) {
      _logger.severe('Error during biometric authentication: $e');
      return BiometricAuthResult.failure('Biometric authentication failed: ${e.toString()}');
    }
  }

  /// Internal method to perform biometric authentication
  Future<bool> _authenticateWithBiometric({required String reason}) async {
    try {
      final bool authenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      
      return authenticated;
    } on PlatformException catch (e) {
      _logger.warning('Platform exception during biometric auth: $e.code - $e.message');
      
      // Handle specific error codes
      switch (e.code) {
        case 'NotAvailable':
          throw BiometricAuthException('Biometric authentication is not available');
        case 'NotEnrolled':
          throw BiometricAuthException('No biometric authentication methods are enrolled');
        case 'LockedOut':
          throw BiometricAuthException('Biometric authentication is temporarily locked');
        case 'PermanentlyLockedOut':
          throw BiometricAuthException('Biometric authentication is permanently locked');
        default:
          throw BiometricAuthException('Biometric authentication failed: $e.message');
      }
    } catch (e) {
      _logger.severe('Unexpected error during biometric auth: $e');
      throw BiometricAuthException('Unexpected error during biometric authentication');
    }
  }

  /// Get a user-friendly description of available biometric types
  String getBiometricTypeDescription(List<BiometricType> types) {
    if (types.isEmpty) return 'No biometric authentication available';
    
    final descriptions = <String>[];
    
    if (types.contains(BiometricType.fingerprint)) {
      descriptions.add('Fingerprint');
    }
    if (types.contains(BiometricType.face)) {
      descriptions.add('Face ID');
    }
    if (types.contains(BiometricType.iris)) {
      descriptions.add('Iris');
    }
    if (types.contains(BiometricType.weak) || types.contains(BiometricType.strong)) {
      descriptions.add('Biometric');
    }
    
    if (descriptions.isEmpty) {
      return 'Biometric authentication';
    } else if (descriptions.length == 1) {
      return descriptions.first;
    } else {
      return descriptions.join(' or ');
    }
  }
}

/// Result class for biometric authentication operations
class BiometricAuthResult {
  final bool success;
  final String message;

  BiometricAuthResult._(this.success, this.message);

  factory BiometricAuthResult.success(String message) => BiometricAuthResult._(true, message);
  factory BiometricAuthResult.failure(String message) => BiometricAuthResult._(false, message);
}

/// Custom exception for biometric authentication errors
class BiometricAuthException implements Exception {
  final String message;
  
  BiometricAuthException(this.message);
  
  @override
  String toString() => 'BiometricAuthException: $message';
}
