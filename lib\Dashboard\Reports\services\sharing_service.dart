import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

import 'package:path_provider/path_provider.dart';
import '../models/report_models.dart';
import 'pdf_export_service.dart';
import 'excel_export_service.dart';

/// Sharing Service
/// 
/// Handles download, share, email, and print functionality for reports.
/// Provides unified interface for all export and sharing operations.
class SharingService {
  static final SharingService _instance = SharingService._internal();
  factory SharingService() => _instance;
  SharingService._internal();

  final PdfExportService _pdfService = PdfExportService();
  final ExcelExportService _excelService = ExcelExportService();

  /// Download PDF report
  Future<void> downloadPDF(ReportData reportData, {ExportConfig? config}) async {
    try {
      _showLoadingDialog('Generating PDF...');
      
      final exportConfig = config ?? ExportConfig.pdf();
      final file = await _pdfService.generatePDF(reportData, exportConfig);
      
      Get.back(); // Close loading dialog
      
      await _saveToDownloads(file, '${reportData.title}.pdf');
      _showDownloadSuccessWithOpen(file, 'PDF saved to Downloads');
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Failed to generate PDF', e.toString());
    }
  }

  /// Download Excel report
  Future<void> downloadExcel(ReportData reportData, {ExportConfig? config}) async {
    try {
      _showLoadingDialog('Generating Excel...');
      
      final exportConfig = config ?? ExportConfig.excel();
      final file = await _excelService.generateExcel(reportData, exportConfig);
      
      Get.back(); // Close loading dialog
      
      await _saveToDownloads(file, '${reportData.title}.xlsx');
      _showDownloadSuccessWithOpen(file, 'Excel saved to Downloads');
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Failed to generate Excel', e.toString());
    }
  }

  /// Share PDF report
  Future<void> sharePDF(ReportData reportData, {ExportConfig? config}) async {
    try {
      _showLoadingDialog('Preparing PDF for sharing...');
      
      final exportConfig = config ?? ExportConfig.pdf(type: ExportType.share);
      final file = await _pdfService.generatePDF(reportData, exportConfig);
      
      Get.back(); // Close loading dialog
      
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Farm Report - ${reportData.title}',
        subject: 'Farm Report: ${reportData.title}',
      );
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Failed to share PDF', e.toString());
    }
  }

  /// Share Excel report
  Future<void> shareExcel(ReportData reportData, {ExportConfig? config}) async {
    try {
      _showLoadingDialog('Preparing Excel for sharing...');
      
      final exportConfig = config ?? ExportConfig.excel(type: ExportType.share);
      final file = await _excelService.generateExcel(reportData, exportConfig);
      
      Get.back(); // Close loading dialog
      
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Farm Report - ${reportData.title}',
        subject: 'Farm Report: ${reportData.title}',
      );
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Failed to share Excel', e.toString());
    }
  }

  /// Email PDF report
  Future<void> emailPDF(ReportData reportData, {ExportConfig? config}) async {
    try {
      _showLoadingDialog('Preparing email...');
      
      final exportConfig = config ?? ExportConfig.pdf(type: ExportType.email);
      final file = await _pdfService.generatePDF(reportData, exportConfig);
      
      Get.back(); // Close loading dialog
      
      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'Farm Report: ${reportData.title}',
        text: 'Please find attached the farm report for ${reportData.title}.\n\n'
              'Generated on: ${_formatDateTime(reportData.generated)}\n'
              '${reportData.startDate != null ? 'Report Period: ${reportData.dateRangeString}\n' : ''}'
              '\nThis report was generated by Cattle Manager App.',
      );
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Failed to email report', e.toString());
    }
  }

  /// Email Excel report
  Future<void> emailExcel(ReportData reportData, {ExportConfig? config}) async {
    try {
      _showLoadingDialog('Preparing email...');
      
      final exportConfig = config ?? ExportConfig.excel(type: ExportType.email);
      final file = await _excelService.generateExcel(reportData, exportConfig);
      
      Get.back(); // Close loading dialog
      
      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'Farm Report: ${reportData.title}',
        text: 'Please find attached the farm report for ${reportData.title}.\n\n'
              'Generated on: ${_formatDateTime(reportData.generated)}\n'
              '${reportData.startDate != null ? 'Report Period: ${reportData.dateRangeString}\n' : ''}'
              '\nThis report was generated by Cattle Manager App.',
      );
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Failed to email report', e.toString());
    }
  }

  /// Print PDF report
  Future<void> printPDF(ReportData reportData, {ExportConfig? config}) async {
    try {
      _showLoadingDialog('Preparing for printing...');
      
      final exportConfig = config ?? ExportConfig.pdf(type: ExportType.print);
      final file = await _pdfService.generatePDF(reportData, exportConfig);
      
      Get.back(); // Close loading dialog
      
      // Show success message for print
      Get.snackbar(
        'Print Ready',
        'PDF generated successfully. File saved at: ${file.path}',
        duration: const Duration(seconds: 5),
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green[800],
      );
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Failed to prepare for printing', e.toString());
    }
  }

  /// Batch export multiple formats
  Future<void> batchExport(
    ReportData reportData, 
    List<ExportFormat> formats,
    {ExportConfig? config}
  ) async {
    try {
      _showLoadingDialog('Generating multiple formats...');
      
      final files = <File>[];
      
      for (final format in formats) {
        switch (format) {
          case ExportFormat.pdf:
            final pdfConfig = config ?? ExportConfig.pdf();
            final file = await _pdfService.generatePDF(reportData, pdfConfig);
            files.add(file);
            break;
          case ExportFormat.excel:
            final excelConfig = config ?? ExportConfig.excel();
            final file = await _excelService.generateExcel(reportData, excelConfig);
            files.add(file);
            break;
        }
      }
      
      Get.back(); // Close loading dialog
      
      // Save all files to downloads
      for (final file in files) {
        final extension = file.path.split('.').last;
        await _saveToDownloads(file, '${reportData.title}.$extension');
      }
      
      Get.snackbar(
        'Batch Export Complete',
        '${files.length} files saved to Downloads',
        duration: const Duration(seconds: 4),
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green[800],
        mainButton: TextButton(
          onPressed: () => _openDownloadsFolder(),
          child: const Text('OPEN FOLDER'),
        ),
      );
      
    } catch (e) {
      Get.back(); // Close loading dialog
      _showErrorDialog('Batch export failed', e.toString());
    }
  }

  /// Get available sharing options
  List<ShareOption> getAvailableShareOptions() {
    return [
      const ShareOption(
        title: 'Download PDF',
        icon: Icons.picture_as_pdf,
        color: Colors.red,
        action: ShareAction.download,
        format: ExportFormat.pdf,
      ),
      const ShareOption(
        title: 'Download Excel',
        icon: Icons.table_chart,
        color: Colors.green,
        action: ShareAction.download,
        format: ExportFormat.excel,
      ),
      const ShareOption(
        title: 'Share PDF',
        icon: Icons.share,
        color: Colors.blue,
        action: ShareAction.share,
        format: ExportFormat.pdf,
      ),
      const ShareOption(
        title: 'Share Excel',
        icon: Icons.share,
        color: Colors.blue,
        action: ShareAction.share,
        format: ExportFormat.excel,
      ),
      const ShareOption(
        title: 'Email PDF',
        icon: Icons.email,
        color: Colors.orange,
        action: ShareAction.email,
        format: ExportFormat.pdf,
      ),
      const ShareOption(
        title: 'Email Excel',
        icon: Icons.email,
        color: Colors.orange,
        action: ShareAction.email,
        format: ExportFormat.excel,
      ),
      const ShareOption(
        title: 'Print PDF',
        icon: Icons.print,
        color: Colors.purple,
        action: ShareAction.print,
        format: ExportFormat.pdf,
      ),
    ];
  }

  /// Execute share option
  Future<void> executeShareOption(
    ShareOption option, 
    ReportData reportData,
    {ExportConfig? config}
  ) async {
    switch (option.action) {
      case ShareAction.download:
        if (option.format == ExportFormat.pdf) {
          await downloadPDF(reportData, config: config);
        } else {
          await downloadExcel(reportData, config: config);
        }
        break;
      case ShareAction.share:
        if (option.format == ExportFormat.pdf) {
          await sharePDF(reportData, config: config);
        } else {
          await shareExcel(reportData, config: config);
        }
        break;
      case ShareAction.email:
        if (option.format == ExportFormat.pdf) {
          await emailPDF(reportData, config: config);
        } else {
          await emailExcel(reportData, config: config);
        }
        break;
      case ShareAction.print:
        await printPDF(reportData, config: config);
        break;
    }
  }

  // Private helper methods

  Future<void> _saveToDownloads(File file, String fileName) async {
    try {
      // On mobile, files are typically saved to app documents directory
      // The file is already in the correct location from the export services
      // This method can be extended for platform-specific download folder handling
    } catch (e) {
      throw Exception('Failed to save file: $e');
    }
  }

  void _showDownloadSuccessWithOpen(File file, String message) {
    Get.snackbar(
      'Download Complete',
      message,
      duration: const Duration(seconds: 5),
      backgroundColor: Colors.green.withValues(alpha: 0.1),
      colorText: Colors.green[800],
      mainButton: TextButton(
        onPressed: () => Get.snackbar('File Location', 'File saved at: ${file.path}'),
        child: const Text('OPEN', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  void _showLoadingDialog(String message) {
    Get.dialog(
      AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }

  void _showErrorDialog(String title, String message) {
    Get.dialog(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _openDownloadsFolder() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      Get.snackbar(
        'Downloads Folder',
        'Files are saved in: ${directory.path}',
        duration: const Duration(seconds: 5),
      );
    } catch (e) {
      _showErrorDialog('Error', 'Could not access downloads folder');
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// Share option model
class ShareOption {
  final String title;
  final IconData icon;
  final Color color;
  final ShareAction action;
  final ExportFormat format;

  const ShareOption({
    required this.title,
    required this.icon,
    required this.color,
    required this.action,
    required this.format,
  });
}