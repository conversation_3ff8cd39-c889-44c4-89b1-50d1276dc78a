import 'dart:collection';
import 'package:logging/logging.dart';

import '../models/notification_isar.dart';

/// Simple LRU cache for notifications
class NotificationCacheService {
  final Logger _logger = Logger('NotificationCacheService');
  final int _maxSize;
  final LinkedHashMap<String, NotificationIsar> _cache = LinkedHashMap();
  final LinkedHashMap<String, List<NotificationIsar>> _listCache = LinkedHashMap();

  NotificationCacheService({int maxSize = 100}) : _maxSize = maxSize;

  /// Get notification from cache
  NotificationIsar? getNotification(String id) {
    final notification = _cache[id];
    if (notification != null) {
      // Move to end (most recently used)
      _cache.remove(id);
      _cache[id] = notification;
      _logger.fine('Cache hit for notification: $id');
    }
    return notification;
  }

  /// Put notification in cache
  void putNotification(String id, NotificationIsar notification) {
    if (_cache.containsKey(id)) {
      _cache.remove(id);
    } else if (_cache.length >= _maxSize) {
      // Remove least recently used
      final firstKey = _cache.keys.first;
      _cache.remove(firstKey);
    }
    
    _cache[id] = notification;
    _logger.fine('Cached notification: $id');
  }

  /// Get notification list from cache
  List<NotificationIsar>? getNotificationList(String key) {
    final list = _listCache[key];
    if (list != null) {
      // Move to end (most recently used)
      _listCache.remove(key);
      _listCache[key] = list;
      _logger.fine('Cache hit for notification list: $key');
    }
    return list;
  }

  /// Put notification list in cache
  void putNotificationList(String key, List<NotificationIsar> notifications) {
    if (_listCache.containsKey(key)) {
      _listCache.remove(key);
    } else if (_listCache.length >= _maxSize) {
      // Remove least recently used
      final firstKey = _listCache.keys.first;
      _listCache.remove(firstKey);
    }
    
    _listCache[key] = List.from(notifications);
    _logger.fine('Cached notification list: $key');
  }

  /// Remove notification from cache
  void removeNotification(String id) {
    _cache.remove(id);
    _invalidateListCache();
    _logger.fine('Removed notification from cache: $id');
  }

  /// Invalidate list cache (when data changes)
  void _invalidateListCache() {
    _listCache.clear();
    _logger.fine('Invalidated list cache');
  }

  /// Clear all cache
  void clear() {
    _cache.clear();
    _listCache.clear();
    _logger.info('Cleared all cache');
  }

  /// Get cache statistics
  Map<String, dynamic> getStats() {
    return {
      'notificationCacheSize': _cache.length,
      'listCacheSize': _listCache.length,
      'maxSize': _maxSize,
    };
  }
}