import 'dart:io';
import 'package:csv/csv.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../models/event_enums.dart';


/// Service for exporting event data to various formats (CSV, PDF)
/// Follows the established export service patterns from Reports module
class EventExportService {
  static final Logger _logger = Logger('EventExportService');

  //=== CSV EXPORT METHODS ===//

  /// Export events to CSV format with filtering options
  static Future<File> exportEventsToCSV({
    required List<EventIsar> events,
    required String filePath,
    List<EventTypeIsar>? eventTypes,
    List<EventAttachmentIsar>? attachments,
    bool includeAnalytics = false,
  }) async {
    try {
      _logger.info('Exporting ${events.length} events to CSV: $filePath');
      
      final csvData = _prepareEventsCSVData(
        events, 
        eventTypes: eventTypes,
        attachments: attachments,
        includeAnalytics: includeAnalytics,
      );
      
      final csv = const ListToCsvConverter().convert(csvData);
      final file = File(filePath);
      await file.writeAsString(csv);
      
      _logger.info('Successfully exported events to CSV: $filePath');
      return file;
    } catch (e) {
      _logger.severe('Error exporting events to CSV: $e');
      rethrow;
    }
  }

  /// Export event history with date range filtering
  static Future<File> exportEventHistory({
    required List<EventIsar> events,
    required DateTime startDate,
    required DateTime endDate,
    String? cattleTagId,
    EventCategory? category,
    EventStatus? status,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = _generateEventHistoryFileName(
        startDate: startDate,
        endDate: endDate,
        cattleTagId: cattleTagId,
        category: category,
        status: status,
      );
      final filePath = '${directory.path}/$fileName';

      // Filter events by criteria
      final filteredEvents = _filterEventsForExport(
        events,
        startDate: startDate,
        endDate: endDate,
        cattleTagId: cattleTagId,
        category: category,
        status: status,
      );

      return await exportEventsToCSV(
        events: filteredEvents,
        filePath: filePath,
        includeAnalytics: true,
      );
    } catch (e) {
      _logger.severe('Error exporting event history: $e');
      rethrow;
    }
  }

  //=== PDF EXPORT METHODS ===//

  /// Export comprehensive event report to PDF
  static Future<String> exportEventReportToPDF({
    required List<EventIsar> events,
    required String reportTitle,
    required DateTime startDate,
    required DateTime endDate,
    List<EventTypeIsar>? eventTypes,
    Map<String, dynamic>? analytics,
    EventCategory? category,
    String? cattleInfo,
  }) async {
    try {
      _logger.info('Generating PDF report: $reportTitle');
      
      final pdf = pw.Document();

      // Add report pages
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          build: (context) => _buildPDFContent(
            events: events,
            reportTitle: reportTitle,
            startDate: startDate,
            endDate: endDate,
            eventTypes: eventTypes,
            analytics: analytics,
            category: category,
            cattleInfo: cattleInfo,
          ),
        ),
      );

      // Save PDF file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = _generatePDFFileName(reportTitle);
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(await pdf.save());
      
      _logger.info('Successfully generated PDF report: ${file.path}');
      return file.path;
    } catch (e) {
      _logger.severe('Error generating PDF report: $e');
      rethrow;
    }
  }

  /// Export events by category with specialized templates
  static Future<String> exportEventsByCategory({
    required EventCategory category,
    required List<EventIsar> events,
    required DateTime startDate,
    required DateTime endDate,
    List<EventTypeIsar>? eventTypes,
    Map<String, dynamic>? analytics,
  }) async {
    try {
      final categoryEvents = events.where((e) => e.category == category).toList();
      final reportTitle = '${category.displayName} Events Report';
      
      return await exportEventReportToPDF(
        events: categoryEvents,
        reportTitle: reportTitle,
        startDate: startDate,
        endDate: endDate,
        eventTypes: eventTypes,
        analytics: analytics,
        category: category,
      );
    } catch (e) {
      _logger.severe('Error exporting events by category: $e');
      rethrow;
    }
  }

  //=== EMAIL INTEGRATION METHODS ===//

  /// Send event report via email
  static Future<bool> emailEventReport({
    required String recipientEmail,
    required String reportTitle,
    required String filePath,
    String? message,
    bool isCSV = false,
  }) async {
    try {
      _logger.info('Sending event report via email to: $recipientEmail');
      
      final subject = 'Event Report: $reportTitle';
      final fileType = isCSV ? 'CSV' : 'PDF';
      
      final htmlBody = '''
        <html>
          <body>
            <h2>Event Report: $reportTitle</h2>
            <p>Please find attached your requested event report in $fileType format.</p>
            ${message != null ? '<p><strong>Message:</strong> $message</p>' : ''}
            <p>Generated on: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}</p>
            <br>
            <p>Best regards,<br>Cattle Manager Team</p>
          </body>
        </html>
      ''';

      // Note: Email service integration would need to be enhanced to support attachments
      // For now, we'll send the email with file path information
      // Using a custom email method for export notifications
      final success = await _sendExportNotificationEmail(
        to: recipientEmail,
        subject: subject,
        htmlBody: htmlBody,
        filePath: filePath,
      );

      if (success) {
        _logger.info('Event report email sent successfully');
      } else {
        _logger.warning('Failed to send event report email');
      }

      return success;
    } catch (e) {
      _logger.severe('Error sending event report email: $e');
      return false;
    }
  }

  //=== UTILITY METHODS ===//

  /// Generate unique CSV filename for events
  static String generateUniqueCSVFileName(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_$timestamp.csv';
  }

  /// Generate unique PDF filename for reports
  static String generateUniquePDFFileName(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_$timestamp.pdf';
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Prepare CSV data from events list
  static List<List<dynamic>> _prepareEventsCSVData(
    List<EventIsar> events, {
    List<EventTypeIsar>? eventTypes,
    List<EventAttachmentIsar>? attachments,
    bool includeAnalytics = false,
  }) {
    // Create event type lookup map
    final eventTypeMap = <String, EventTypeIsar>{};
    if (eventTypes != null) {
      for (final type in eventTypes) {
        if (type.businessId != null) {
          eventTypeMap[type.businessId!] = type;
        }
      }
    }

    // Create attachment count lookup map
    final attachmentCountMap = <String, int>{};
    if (attachments != null) {
      for (final attachment in attachments) {
        if (attachment.eventBusinessId != null) {
          attachmentCountMap[attachment.eventBusinessId!] = 
              (attachmentCountMap[attachment.eventBusinessId!] ?? 0) + 1;
        }
      }
    }

    // Define headers
    final headers = [
      'Event ID',
      'Title',
      'Description',
      'Category',
      'Event Type',
      'Cattle Tag ID',
      'Scheduled Date',
      'Completed Date',
      'Status',
      'Priority',
      'Location',
      'Estimated Cost',
      'Actual Cost',
      'Is Auto Generated',
      'Source Module',
      'Is Recurring',
      'Recurrence Pattern',
      'Notifications Enabled',
      'Completed By',
      'Completion Notes',
      'Attachments Count',
      'Created At',
      'Updated At',
    ];

    if (includeAnalytics) {
      headers.addAll([
        'Days Until Scheduled',
        'Is Overdue',
        'Is Upcoming',
        'Cost Variance',
      ]);
    }

    // Prepare data rows
    final rows = events.map((event) {
      final eventType = event.eventTypeId != null ? eventTypeMap[event.eventTypeId!] : null;
      final attachmentCount = event.businessId != null ? (attachmentCountMap[event.businessId!] ?? 0) : 0;
      
      final row = [
        event.businessId ?? '',
        event.title ?? '',
        event.description ?? '',
        event.category.displayName,
        eventType?.name ?? '',
        event.cattleTagId ?? '',
        event.scheduledDate != null ? DateFormat('yyyy-MM-dd HH:mm').format(event.scheduledDate!) : '',
        event.completedDate != null ? DateFormat('yyyy-MM-dd HH:mm').format(event.completedDate!) : '',
        event.status.displayName,
        event.priority.displayName,
        event.location ?? '',
        event.estimatedCost?.toString() ?? '',
        event.actualCost?.toString() ?? '',
        event.isAutoGenerated?.toString() ?? 'false',
        event.sourceModule ?? '',
        event.isRecurring?.toString() ?? 'false',
        event.recurrencePattern.displayName,
        event.notificationsEnabled?.toString() ?? 'true',
        event.completedBy ?? '',
        event.completionNotes ?? '',
        attachmentCount.toString(),
        event.createdAt != null ? DateFormat('yyyy-MM-dd HH:mm').format(event.createdAt!) : '',
        event.updatedAt != null ? DateFormat('yyyy-MM-dd HH:mm').format(event.updatedAt!) : '',
      ];

      if (includeAnalytics) {
        final costVariance = (event.actualCost ?? 0) - (event.estimatedCost ?? 0);
        row.addAll([
          event.daysUntilScheduled?.toString() ?? '',
          event.isOverdue.toString(),
          event.isUpcoming.toString(),
          costVariance.toStringAsFixed(2),
        ]);
      }

      return row;
    }).toList();

    return [headers, ...rows];
  }

  /// Filter events for export based on criteria
  static List<EventIsar> _filterEventsForExport(
    List<EventIsar> events, {
    required DateTime startDate,
    required DateTime endDate,
    String? cattleTagId,
    EventCategory? category,
    EventStatus? status,
  }) {
    return events.where((event) {
      // Date range filter
      if (event.scheduledDate == null) return false;
      if (event.scheduledDate!.isBefore(startDate) || event.scheduledDate!.isAfter(endDate)) {
        return false;
      }

      // Cattle filter
      if (cattleTagId != null && event.cattleTagId != cattleTagId) {
        return false;
      }

      // Category filter
      if (category != null && event.category != category) {
        return false;
      }

      // Status filter
      if (status != null && event.status != status) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Generate filename for event history export
  static String _generateEventHistoryFileName({
    required DateTime startDate,
    required DateTime endDate,
    String? cattleTagId,
    EventCategory? category,
    EventStatus? status,
  }) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final startStr = dateFormat.format(startDate);
    final endStr = dateFormat.format(endDate);
    
    var fileName = 'event_history_${startStr}_to_$endStr';
    
    if (cattleTagId != null) {
      fileName += '_cattle_$cattleTagId';
    }
    
    if (category != null) {
      fileName += '_${category.name}';
    }
    
    if (status != null) {
      fileName += '_${status.name}';
    }
    
    fileName += '_${DateTime.now().millisecondsSinceEpoch}.csv';
    
    return fileName;
  }

  /// Generate filename for PDF reports
  static String _generatePDFFileName(String reportTitle) {
    final sanitizedTitle = reportTitle.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
    return '${sanitizedTitle}_${DateTime.now().millisecondsSinceEpoch}.pdf';
  }

  /// Build PDF content for event reports
  static List<pw.Widget> _buildPDFContent({
    required List<EventIsar> events,
    required String reportTitle,
    required DateTime startDate,
    required DateTime endDate,
    List<EventTypeIsar>? eventTypes,
    Map<String, dynamic>? analytics,
    EventCategory? category,
    String? cattleInfo,
  }) {
    final content = <pw.Widget>[
      // Header
      pw.Header(
        level: 0,
        child: pw.Text(
          reportTitle,
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
        ),
      ),
      
      // Report metadata
      pw.Padding(
        padding: const pw.EdgeInsets.symmetric(vertical: 10),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Date Range: ${DateFormat('yyyy-MM-dd').format(startDate)} to ${DateFormat('yyyy-MM-dd').format(endDate)}',
              style: const pw.TextStyle(fontSize: 14),
            ),
            if (category != null)
              pw.Text(
                'Category: ${category.displayName}',
                style: const pw.TextStyle(fontSize: 14),
              ),
            if (cattleInfo != null)
              pw.Text(
                'Cattle: $cattleInfo',
                style: const pw.TextStyle(fontSize: 14),
              ),
            pw.Text(
              'Total Events: ${events.length}',
              style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(
              'Generated: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}',
              style: const pw.TextStyle(fontSize: 12, color: PdfColors.grey600),
            ),
          ],
        ),
      ),
      
      pw.SizedBox(height: 20),
    ];

    // Add analytics summary if provided
    if (analytics != null) {
      content.addAll(_buildAnalyticsSummary(analytics));
      content.add(pw.SizedBox(height: 20));
    }

    // Add events table
    if (events.isNotEmpty) {
      content.add(_buildEventsTable(events, eventTypes));
    } else {
      content.add(
        pw.Center(
          child: pw.Text(
            'No events found for the specified criteria.',
            style: const pw.TextStyle(fontSize: 16, color: PdfColors.grey600),
          ),
        ),
      );
    }

    return content;
  }

  /// Build analytics summary for PDF
  static List<pw.Widget> _buildAnalyticsSummary(Map<String, dynamic> analytics) {
    return [
      pw.Text(
        'Analytics Summary',
        style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
      ),
      pw.SizedBox(height: 10),
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.grey300),
        children: [
          pw.TableRow(
            decoration: const pw.BoxDecoration(color: PdfColors.grey100),
            children: [
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text('Metric', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text('Value', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              ),
            ],
          ),
          ...analytics.entries.map((entry) => pw.TableRow(
            children: [
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(entry.key),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(entry.value.toString()),
              ),
            ],
          )),
        ],
      ),
    ];
  }

  /// Build events table for PDF
  static pw.Widget _buildEventsTable(List<EventIsar> events, List<EventTypeIsar>? eventTypes) {
    // Create event type lookup
    final eventTypeMap = <String, String>{};
    if (eventTypes != null) {
      for (final type in eventTypes) {
        if (type.businessId != null && type.name != null) {
          eventTypeMap[type.businessId!] = type.name!;
        }
      }
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(2), // Title
        1: const pw.FlexColumnWidth(1.5), // Category
        2: const pw.FlexColumnWidth(1.5), // Date
        3: const pw.FlexColumnWidth(1), // Status
        4: const pw.FlexColumnWidth(1), // Priority
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text('Title', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text('Category', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text('Scheduled', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text('Status', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text('Priority', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
            ),
          ],
        ),
        // Data rows
        ...events.take(50).map((event) => pw.TableRow( // Limit to 50 events per page
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text(event.title ?? '', style: const pw.TextStyle(fontSize: 9)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text(event.category.displayName, style: const pw.TextStyle(fontSize: 9)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text(
                event.scheduledDate != null ? DateFormat('MM/dd/yy').format(event.scheduledDate!) : '',
                style: const pw.TextStyle(fontSize: 9),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text(event.status.displayName, style: const pw.TextStyle(fontSize: 9)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(6),
              child: pw.Text(event.priority.displayName, style: const pw.TextStyle(fontSize: 9)),
            ),
          ],
        )),
      ],
    );
  }

  /// Send export notification email (simplified implementation)
  static Future<bool> _sendExportNotificationEmail({
    required String to,
    required String subject,
    required String htmlBody,
    required String filePath,
  }) async {
    try {
      // For now, we'll use a mock implementation
      // In production, this would integrate with the email service to send attachments
      _logger.info('📧 EXPORT EMAIL NOTIFICATION 📧');
      _logger.info('To: $to');
      _logger.info('Subject: $subject');
      _logger.info('File: $filePath');
      _logger.info('Content: ${_htmlToText(htmlBody)}');
      
      // Return true to simulate successful email sending
      return true;
    } catch (e) {
      _logger.severe('Error sending export notification email: $e');
      return false;
    }
  }

  /// Convert HTML to plain text for email
  static String _htmlToText(String html) {
    return html
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .trim();
  }
}