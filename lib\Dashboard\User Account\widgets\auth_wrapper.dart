import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../../services/logging_service.dart';
import '../services/auth_service.dart';
import '../screens/welcome_screen.dart';
import '../guards/demo_guard.dart';
import '../../dashboard_screen.dart';


/// Authentication wrapper that determines what screen to show based on auth state
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> with WidgetsBindingObserver {
  bool _isLoading = true;
  AuthState _authState = AuthState.unauthenticated;

  // Safe getter for LoggingService that handles initialization issues
  LoggingService? get _loggingService {
    try {
      return GetIt.instance.isRegistered<LoggingService>()
          ? GetIt.instance<LoggingService>()
          : null;
    } catch (e) {
      return null;
    }
  }

  @override
  void initState() {
    super.initState();
    _loggingService?.info('🚀 [AUTH_WRAPPER] AuthWrapper initialized, starting auth state check...');
    WidgetsBinding.instance.addObserver(this);
    _checkAuthState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _loggingService?.info('🔄 [AUTH_WRAPPER] App lifecycle state changed to: $state');
    if (state == AppLifecycleState.resumed) {
      // Recheck auth state when app resumes
      _loggingService?.info('🔄 [AUTH_WRAPPER] App resumed, rechecking auth state...');
      _checkAuthState();
    }
  }

  Future<void> _checkAuthState() async {
    _loggingService?.info('🔍 [AUTH_WRAPPER] Starting auth state check...');

    try {
      // Small delay to ensure services are initialized
      await Future.delayed(const Duration(milliseconds: 100));

      final authService = GetIt.instance<AuthService>();

      _loggingService?.info('🔍 [AUTH_WRAPPER] AuthService.isAuthenticated: $authService.isAuthenticated');
      _loggingService?.info('🔍 [AUTH_WRAPPER] DemoGuard.isDemoModeActive(): ${DemoGuard.isDemoModeActive()}');

      // Note: Welcome screen preference checking removed as SettingsRepository was deleted
      // For now, we'll show welcome screen for new users and skip for authenticated users
      bool shouldShowWelcome = !authService.isAuthenticated && !DemoGuard.isDemoModeActive();
      _loggingService?.info('🔍 [AUTH_WRAPPER] shouldShowWelcome: $shouldShowWelcome (based on auth status)');

      AuthState newAuthState;
      if (authService.isAuthenticated) {
        newAuthState = AuthState.authenticated;
        _loggingService?.info('✅ [AUTH_WRAPPER] User is authenticated - routing to dashboard');
      } else if (DemoGuard.isDemoModeActive()) {
        newAuthState = AuthState.demo;
        _loggingService?.info('✅ [AUTH_WRAPPER] Demo mode is active - routing to demo dashboard');
      } else if (shouldShowWelcome) {
        // Show welcome screen for first-time users or when preference is set
        newAuthState = AuthState.unauthenticated;
        _loggingService?.info('✅ [AUTH_WRAPPER] Should show welcome screen - routing to welcome');
      } else {
        // This case shouldn't normally happen, but fallback to unauthenticated
        newAuthState = AuthState.unauthenticated;
        _loggingService?.info('⚠️ [AUTH_WRAPPER] Fallback case - routing to welcome screen');
      }

      setState(() {
        _authState = newAuthState;
        _isLoading = false;
      });

      _loggingService?.info('🎯 [AUTH_WRAPPER] Final auth state decision: $_authState');
    } catch (e) {
      // If there's an error, default to unauthenticated (which shows welcome screen)
      _loggingService?.error('❌ [AUTH_WRAPPER] Error during auth state check: $e');
      setState(() {
        _authState = AuthState.unauthenticated;
        _isLoading = false;
      });
      _loggingService?.info('🎯 [AUTH_WRAPPER] Error fallback - routing to welcome screen');
    }
  }

  /// Method to refresh auth state (can be called externally)
  void refreshAuthState() {
    _checkAuthState();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      _loggingService?.info('⏳ [AUTH_WRAPPER] Showing loading screen...');
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    _loggingService?.info('🎨 [AUTH_WRAPPER] Building UI for auth state: $_authState');

    switch (_authState) {
      case AuthState.authenticated:
        _loggingService?.info('🏠 [AUTH_WRAPPER] Rendering DashboardScreen (authenticated)');
        return const DashboardScreen();
      case AuthState.demo:
        _loggingService?.info('🏠 [AUTH_WRAPPER] Rendering DashboardScreen (demo mode)');
        return const DemoGuard(
          authState: AuthState.demo,
          child: DashboardScreen(),
        );
      case AuthState.unauthenticated:
        _loggingService?.info('👋 [AUTH_WRAPPER] Rendering WelcomeScreen');
        return const WelcomeScreen();
    }
  }
}


