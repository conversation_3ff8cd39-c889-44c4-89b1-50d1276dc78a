import 'package:isar/isar.dart';

part 'notification_operation.g.dart';

/// Enum for notification operation types
enum NotificationOperationType {
  create,
  update,
  delete,
  markAsRead,
  markAsUnread,
}

/// Model for queued notification operations during offline mode
@collection
class NotificationOperation {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true)
  String? operationId;
  
  @enumerated
  NotificationOperationType type = NotificationOperationType.create;
  
  String? notificationId;
  String? operationData; // JSON string of operation data
  
  DateTime? createdAt;
  DateTime? scheduledFor;
  
  int retryCount = 0;
  int maxRetries = 3;
  
  bool isProcessed = false;
  DateTime? processedAt;
  
  String? errorMessage;
  
  NotificationOperation();
}