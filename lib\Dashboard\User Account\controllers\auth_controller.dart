import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../services/auth_service.dart';
import '../services/biometric_auth_service.dart';
import '../services/error_handling_service.dart';
import '../services/google_auth_service.dart';

import '../services/token_service.dart';
import '../models/user_isar.dart';

/// Controller for managing authentication state and operations
class AuthController extends ChangeNotifier {
  final Logger _logger = Logger('AuthController');
  final AuthService _authService = AuthService();
  final GoogleAuthService _googleAuthService = GoogleAuthService();
  final ErrorHandlingService _errorHandler = ErrorHandlingService();

  final TokenService _tokenService = TokenService();

  // State variables
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;
  bool _isInitialized = false;

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  bool get isAuthenticated => _authService.isAuthenticated;
  UserIsar? get currentUser => _authService.currentUser;
  bool get isInitialized => _isInitialized;

  /// Initialize the auth controller
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _setLoading(true);
    try {
      await _authService.initialize();
      _isInitialized = true;
      _logger.info('AuthController initialized');
    } catch (e) {
      _logger.severe('Error initializing AuthController: $e');
      _setError('Failed to initialize authentication');
    } finally {
      _setLoading(false);
    }
  }

  /// Login with email/username and password
  Future<bool> login({
    required String emailOrUsername,
    required String password,
    bool rememberMe = false,
    String? deviceInfo,
    String? ipAddress,
    String? userAgent,
    String? location,
  }) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _authService.login(
        emailOrUsername: emailOrUsername,
        password: password,
        rememberMe: rememberMe,
        deviceInfo: deviceInfo,
        ipAddress: ipAddress,
        userAgent: userAgent,
        location: location,
      );

      if (result.success) {
        _setSuccess(result.message);
        _logger.info('Login successful for: $emailOrUsername');
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Login error: $e');
      _errorHandler.logError('login', e);
      _setError(_errorHandler.handleAuthError(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Register a new user account
  Future<bool> register({
    required String email,
    required String username,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
    String? farmName,
    String? farmLocation,
    String? farmDescription,
  }) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _authService.register(
        email: email,
        username: username,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        farmName: farmName,
        farmLocation: farmLocation,
        farmDescription: farmDescription,
      );

      if (result.success) {
        _setSuccess(result.message);
        _logger.info('Registration successful for: $email');
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Registration error: $e');
      _errorHandler.logError('registration', e);
      _setError(_errorHandler.handleAuthError(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign in with Google
  Future<bool> signInWithGoogle({bool rememberMe = false}) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _googleAuthService.signIn(rememberMe: rememberMe);

      if (result.success) {
        _setSuccess(result.message);
        _logger.info('Google Sign-In successful');
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Google Sign-In error: $e');
      _errorHandler.logError('google_signin', e);
      _setError(_errorHandler.handleAuthError(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up with Google
  Future<bool> signUpWithGoogle({bool rememberMe = false}) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _googleAuthService.signUp(rememberMe: rememberMe);

      if (result.success) {
        _setSuccess(result.message);
        _logger.info('Google Sign-Up successful');
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Google Sign-Up error: $e');
      _errorHandler.logError('google_signup', e);
      _setError(_errorHandler.handleAuthError(e));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Logout current user
  Future<void> logout() async {
    _setLoading(true);
    _clearMessages();

    try {
      await _authService.logout();
      await _googleAuthService.signOut(); // Also sign out from Google
      _setSuccess('Logged out successfully');
      _logger.info('User logged out');
    } catch (e) {
      _logger.severe('Logout error: $e');
      _setError('Logout failed');
    } finally {
      _setLoading(false);
    }
  }

  /// Request password reset
  Future<bool> requestPasswordReset(String email) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _authService.requestPasswordReset(email);

      if (result.success) {
        _setSuccess(result.message);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Password reset request error: $e');
      _setError('Failed to request password reset');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Reset password with token
  Future<bool> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _authService.resetPassword(
        email: email,
        token: token,
        newPassword: newPassword,
      );

      if (result.success) {
        _setSuccess(result.message);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Password reset error: $e');
      _setError('Failed to reset password');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Verify email with token
  Future<bool> verifyEmail({
    required String email,
    required String token,
  }) async {
    _setLoading(true);
    _clearMessages();

    try {
      // First validate the token format and expiration
      final tokenValidation = _tokenService.validateEmailVerificationToken(token);

      if (!tokenValidation.isValid) {
        _setError(tokenValidation.error ?? 'Invalid verification token');
        return false;
      }

      // Verify that the token email matches the provided email
      if (tokenValidation.email != email) {
        _setError('Token email mismatch');
        return false;
      }

      final result = await _authService.verifyEmail(
        email: email,
        token: token,
      );

      if (result.success) {
        _setSuccess(result.message);
        // Refresh current user data if this is the current user
        if (currentUser?.email == email) {
          // Trigger a rebuild to reflect the updated email verification status
          notifyListeners();
        }
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Email verification error: $e');
      _setError('Failed to verify email');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Resend email verification
  Future<bool> resendEmailVerification({required String email}) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _authService.resendEmailVerification(email: email);

      if (result.success) {
        _setSuccess(result.message);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Resend email verification error: $e');
      _setError('Failed to resend verification email');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Check if current user needs email verification
  bool get needsEmailVerificationNew {
    return currentUser != null && !currentUser!.isEmailVerified;
  }

  /// Get current user's email verification status
  bool get isEmailVerified {
    return currentUser?.isEmailVerified ?? false;
  }

  /// Change password for authenticated user
  Future<bool> changePassword({
    String? currentPassword,
    required String newPassword,
  }) async {
    _setLoading(true);
    _clearMessages();

    try {
      final result = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.success) {
        _setSuccess(result.message);
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Change password error: $e');
      _setError('Failed to change password');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Clear messages
  void clearMessages() {
    _clearMessages();
    notifyListeners();
  }

  /// Check if user needs email verification
  bool get needsEmailVerification {
    return currentUser?.needsEmailVerification == true;
  }

  /// Check if user account is locked
  bool get isAccountLocked {
    return currentUser?.isLocked == true;
  }

  /// Get remaining lock time in minutes
  int get remainingLockTimeMinutes {
    final user = currentUser;
    if (user?.lockedUntil != null) {
      final remaining = user!.lockedUntil!.difference(DateTime.now()).inMinutes;
      return remaining > 0 ? remaining : 0;
    }
    return 0;
  }

  // Private helper methods

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _successMessage = null;
    notifyListeners();
  }

  void _setSuccess(String message) {
    _successMessage = message;
    _errorMessage = null;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  // Biometric Authentication Methods

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAvailable() async {
    try {
      final biometricService = GetIt.instance<BiometricAuthService>();
      return await biometricService.isBiometricAvailable();
    } catch (e) {
      _logger.warning('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Check if biometric authentication is enabled for the current user
  Future<bool> isBiometricEnabled() async {
    try {
      final biometricService = GetIt.instance<BiometricAuthService>();
      return await biometricService.isBiometricEnabled();
    } catch (e) {
      _logger.warning('Error checking if biometric is enabled: $e');
      return false;
    }
  }

  /// Enable biometric authentication for the current user
  Future<bool> enableBiometricAuth() async {
    if (currentUser == null) {
      _setError('No user is currently logged in');
      return false;
    }

    _setLoading(true);
    _clearMessages();

    try {
      final biometricService = GetIt.instance<BiometricAuthService>();
      final result = await biometricService.enableBiometricAuth(currentUser!);

      if (result.success) {
        _setSuccess(result.message);
        _logger.info('Biometric authentication enabled for user: ${currentUser!.email}');
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Error enabling biometric auth: $e');
      _setError('Failed to enable biometric authentication');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Disable biometric authentication
  Future<bool> disableBiometricAuth() async {
    _setLoading(true);
    _clearMessages();

    try {
      final biometricService = GetIt.instance<BiometricAuthService>();
      final result = await biometricService.disableBiometricAuth();

      if (result.success) {
        _setSuccess(result.message);
        _logger.info('Biometric authentication disabled');
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Error disabling biometric auth: $e');
      _setError('Failed to disable biometric authentication');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Authenticate user with biometric and log them in
  Future<bool> authenticateWithBiometric() async {
    _setLoading(true);
    _clearMessages();

    try {
      final biometricService = GetIt.instance<BiometricAuthService>();
      final result = await biometricService.authenticateAndLogin();

      if (result.success) {
        _setSuccess(result.message);
        _logger.info('Biometric authentication successful');
        notifyListeners(); // Notify listeners of authentication state change
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _logger.severe('Error during biometric authentication: $e');
      _setError('Biometric authentication failed');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Get available biometric types description
  Future<String> getBiometricDescription() async {
    try {
      final biometricService = GetIt.instance<BiometricAuthService>();
      final types = await biometricService.getAvailableBiometrics();
      return biometricService.getBiometricTypeDescription(types);
    } catch (e) {
      _logger.warning('Error getting biometric description: $e');
      return 'Biometric authentication';
    }
  }
}
