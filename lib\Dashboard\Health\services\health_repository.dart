import 'package:isar/isar.dart';

import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../../services/database/isar_service.dart';

import 'package:flutter/foundation.dart';
// Legacy alias for compatibility
typedef HealthHandler = HealthRepository;

/// Pure reactive repository for Health module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
/// Sync functionality moved to dedicated HealthSyncService for clean separation
class HealthRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  HealthRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE HEALTH STREAMS ===//

  /// Watches all health records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<HealthRecordIsar>> watchAllHealthRecords() {
    debugPrint('🔄 HEALTH REPOSITORY: Setting up watchAllHealthRecords stream');
    return _isar.healthRecordIsars.where().watch(fireImmediately: true).map((records) {
      debugPrint('🔄 HEALTH REPOSITORY: watchAllHealthRecords stream fired with $records.length health records');
      return records;
    });
  }

  /// Watches all medications with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<MedicationIsar>> watchAllMedications() {
    debugPrint('🔄 HEALTH REPOSITORY: Setting up watchAllMedications stream');
    return _isar.medicationIsars.where().watch(fireImmediately: true).map((medications) {
      debugPrint('🔄 HEALTH REPOSITORY: watchAllMedications stream fired with $medications.length medications');
      return medications;
    });
  }

  /// Watches all treatments with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<TreatmentIsar>> watchAllTreatments() {
    debugPrint('🔄 HEALTH REPOSITORY: Setting up watchAllTreatments stream');
    return _isar.treatmentIsars.where().watch(fireImmediately: true).map((treatments) {
      debugPrint('🔄 HEALTH REPOSITORY: watchAllTreatments stream fired with $treatments.length treatments');
      return treatments;
    });
  }

  /// Watches all vaccinations with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<VaccinationIsar>> watchAllVaccinations() {
    debugPrint('🔄 HEALTH REPOSITORY: Setting up watchAllVaccinations stream');
    return _isar.vaccinationIsars.where().watch(fireImmediately: true).map((vaccinations) {
      debugPrint('🔄 HEALTH REPOSITORY: watchAllVaccinations stream fired with $vaccinations.length vaccinations');
      return vaccinations;
    });
  }

  //=== HEALTH RECORDS CRUD ===//

  /// Save (add or update) a health record using Isar's native upsert
  Future<void> saveHealthRecord(HealthRecordIsar record) async {
    debugPrint('💾 HEALTH REPOSITORY: Starting saveHealthRecord for ID $record.id');

    await _isar.writeTxn(() async {
      final savedId = await _isar.healthRecordIsars.put(record);
      debugPrint('✅ HEALTH REPOSITORY: Health record saved with ID $savedId');
    });

    // Check count after save
    final countAfterSave = await _isar.healthRecordIsars.count();
    debugPrint('📊 HEALTH REPOSITORY: Total health records count after save: $countAfterSave');
    debugPrint('✅ HEALTH REPOSITORY: saveHealthRecord completed');
  }

  /// Delete a health record by its Isar ID
  Future<void> deleteHealthRecord(int id) async {
    debugPrint('🗑️ HEALTH REPOSITORY: Starting deleteHealthRecord for ID $id');

    // Check if record exists before delete
    final existingRecord = await _isar.healthRecordIsars.get(id);
    debugPrint('📋 HEALTH REPOSITORY: Health record exists before delete: ${existingRecord != null}');

    await _isar.writeTxn(() async {
      final deleteResult = await _isar.healthRecordIsars.delete(id);
      debugPrint('✅ HEALTH REPOSITORY: Delete operation result: $deleteResult (true = deleted, false = not found)');
    });

    // Check count after delete
    final countAfterDelete = await _isar.healthRecordIsars.count();
    debugPrint('📊 HEALTH REPOSITORY: Total health records count after delete: $countAfterDelete');
    debugPrint('✅ HEALTH REPOSITORY: deleteHealthRecord completed for ID $id');
  }

  //=== MEDICATION CRUD ===//

  /// Save (add or update) a medication using Isar's native upsert
  Future<void> saveMedication(MedicationIsar medication) async {
    await _isar.writeTxn(() async {
      await _isar.medicationIsars.put(medication);
    });
  }

  /// Delete a medication by its Isar ID
  Future<void> deleteMedication(int id) async {
    await _isar.writeTxn(() async {
      await _isar.medicationIsars.delete(id);
    });
  }

  //=== TREATMENT CRUD ===//

  /// Save (add or update) a treatment using Isar's native upsert
  Future<void> saveTreatment(TreatmentIsar treatment) async {
    await _isar.writeTxn(() async {
      await _isar.treatmentIsars.put(treatment);
    });
  }

  /// Delete a treatment by its Isar ID
  Future<void> deleteTreatment(int id) async {
    await _isar.writeTxn(() async {
      await _isar.treatmentIsars.delete(id);
    });
  }

  //=== VACCINATION CRUD ===//

  /// Save (add or update) a vaccination using Isar's native upsert
  Future<void> saveVaccination(VaccinationIsar vaccination) async {
    debugPrint('💾 HEALTH REPOSITORY: Starting saveVaccination for ID $vaccination.id');

    await _isar.writeTxn(() async {
      final savedId = await _isar.vaccinationIsars.put(vaccination);
      debugPrint('✅ HEALTH REPOSITORY: Vaccination saved with ID $savedId');
    });

    // Check count after save
    final countAfterSave = await _isar.vaccinationIsars.count();
    debugPrint('📊 HEALTH REPOSITORY: Total vaccinations count after save: $countAfterSave');
    debugPrint('✅ HEALTH REPOSITORY: saveVaccination completed');
  }

  /// Delete a vaccination by its Isar ID
  Future<void> deleteVaccination(int id) async {
    debugPrint('🗑️ HEALTH REPOSITORY: Starting deleteVaccination for ID $id');

    // Check if record exists before delete
    final existingRecord = await _isar.vaccinationIsars.get(id);
    debugPrint('📋 HEALTH REPOSITORY: Record exists before delete: ${existingRecord != null}');

    await _isar.writeTxn(() async {
      final deleteResult = await _isar.vaccinationIsars.delete(id);
      debugPrint('✅ HEALTH REPOSITORY: Delete operation result: $deleteResult (true = deleted, false = not found)');
    });

    // Check count after delete
    final countAfterDelete = await _isar.vaccinationIsars.count();
    debugPrint('📊 HEALTH REPOSITORY: Total vaccinations count after delete: $countAfterDelete');
    debugPrint('✅ HEALTH REPOSITORY: deleteVaccination completed for ID $id');
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get health records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<HealthRecordIsar>> getHealthRecordsByCattleId(String cattleId) async {
    return await _isar.healthRecordIsars
        .filter()
        .cattleTagIdEqualTo(cattleId)
        .findAll();
  }

  /// Get all health records (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<HealthRecordIsar>> getAllHealthRecords() async {
    return await _isar.healthRecordIsars.where().findAll();
  }

  /// Get all medications (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<MedicationIsar>> getAllMedications() async {
    return await _isar.medicationIsars.where().findAll();
  }

  /// Get all treatments (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<TreatmentIsar>> getAllTreatments() async {
    return await _isar.treatmentIsars.where().findAll();
  }

  /// Get all vaccinations (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<VaccinationIsar>> getAllVaccinations() async {
    return await _isar.vaccinationIsars.where().findAll();
  }
}


