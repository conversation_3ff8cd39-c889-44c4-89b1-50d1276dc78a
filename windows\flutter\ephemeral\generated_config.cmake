# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Cattle Manager App" PROJECT_DIR)

set(FLUTTER_VERSION "1.18.0+118" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 18 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 118 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter"
  "PROJECT_DIR=D:\\Cattle Manager App"
  "FLUTTER_ROOT=C:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Cattle Manager App\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Cattle Manager App"
  "FLUTTER_TARGET=D:\\Cattle Manager App\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Cattle Manager App\\.dart_tool\\package_config.json"
)
