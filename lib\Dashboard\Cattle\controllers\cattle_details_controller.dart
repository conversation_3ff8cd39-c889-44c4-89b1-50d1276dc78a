import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'dart:async';

import '../models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../services/cattle_repository.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import '../services/cattle_details_analytics_service.dart';
// Unused repository imports removed
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Breeding/models/breeding_record_isar.dart';
import 'cattle_controller.dart'; // Import for ControllerState enum

/// Controller for individual cattle details screen
/// Manages state for a specific cattle record and its related data
class CattleDetailsController extends ChangeNotifier {
  final String _cattleId;
  late final CattleRepository _cattleRepository;
  late final FarmSetupRepository _farmSetupRepository;
  // Related record repositories for real-time synchronization
  late final Isar _isar;

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - individual streams (following breeding details pattern)
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription<List<MilkRecordIsar>>? _milkRecordsStreamSubscription;
  StreamSubscription<List<HealthRecordIsar>>? _healthRecordsStreamSubscription;
  StreamSubscription<List<BreedingRecordIsar>>? _breedingRecordsStreamSubscription;

  // Core data
  CattleIsar? _cattle;
  CattleIsar? _motherCattle;
  List<CattleIsar> _offspring = [];
  List<CattleIsar> _siblings = [];

  List<AnimalTypeIsar> _animalTypes = [];
  List<BreedCategoryIsar> _breeds = [];

  // Related records for analytics
  List<MilkRecordIsar> _milkRecords = [];
  List<HealthRecordIsar> _healthRecords = [];
  List<BreedingRecordIsar> _breedingRecords = [];

  // Individual analytics
  IndividualCattleAnalyticsResult _individualAnalytics = IndividualCattleAnalyticsResult.empty;

  // Lookup maps for O(1) access
  Map<String, AnimalTypeIsar> _animalTypeMap = {};
  Map<String, BreedCategoryIsar> _breedMap = {};

  // Use lazy getters to avoid accessing GetIt services in constructor
  CattleRepository get _cattleRepository => GetIt.instance<CattleRepository>();
  FarmSetupRepository get _farmSetupRepository => GetIt.instance<FarmSetupRepository>();
  Isar get _isar => GetIt.instance<Isar>();

  // Constructor
  CattleDetailsController(this._cattleId) {
    // Defer initialization to avoid GetIt access in constructor
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialize();
    });
  }

  // Initialize after the widget tree is built
  Future<void> _initialize() async {
    try {
      await _loadSupportingData(); // Load supporting data
      _initializeStreamListener();
    } catch (e) {
      // Error initialization - dependencies not available
      _setError('Failed to initialize: ${e.toString()}');
    }
  }

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  CattleIsar? get cattle => _cattle;
  CattleIsar? get motherCattle => _motherCattle;
  List<CattleIsar> get offspring => List.unmodifiable(_offspring);
  List<CattleIsar> get siblings => List.unmodifiable(_siblings);

  List<AnimalTypeIsar> get animalTypes => List.unmodifiable(_animalTypes);
  List<BreedCategoryIsar> get breeds => List.unmodifiable(_breeds);

  // Analytics getter
  IndividualCattleAnalyticsResult get individualAnalytics => _individualAnalytics;

  // Lookup methods with O(1) performance
  String? getAnimalTypeName(String? id) => _animalTypeMap[id]?.name;
  String? getBreedName(String? id) => _breedMap[id]?.name;
  AnimalTypeIsar? getAnimalType(String? id) => _animalTypeMap[id];
  BreedCategoryIsar? getBreed(String? id) => _breedMap[id];

  /// Get breed for the current cattle
  BreedCategoryIsar? get currentBreed {
    if (_cattle?.breedId == null) return null;
    return _breedMap[_cattle!.breedId!];
  }



  /// Load supporting data (animal types, breeds)
  Future<void> _loadSupportingData() async {
    await Future.wait([
      _loadAnimalTypes(),
      _loadBreeds(),
    ]);
  }

  /// Initialize stream listener for real-time updates of the specific cattle
  void _initializeStreamListener() {
    // Watch for changes to the specific cattle record
    _cattleStreamSubscription = _isar.cattleIsars
        .filter()
        .businessIdEqualTo(_cattleId)
        .watch(fireImmediately: true)
        .listen((cattleList) {
      final cattle = cattleList.isNotEmpty ? cattleList.first : null;
      _handleCattleUpdate(cattle);
    });
  }

  /// Initialize stream listeners for real-time updates of related records
  /// Following the breeding details pattern using individual streams instead of StreamZip
  void _initializeRelatedRecordsStreams() {
    if (_cattle?.businessId == null) return;

    debugPrint('🔧 CATTLE DETAILS: Initializing individual related record streams for cattle: ${_cattle!.tagId} (businessId: ${_cattle!.businessId})');

    // Milk records stream
    _milkRecordsStreamSubscription = _isar.milkRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((milkRecords) {
      debugPrint('🔄 CATTLE DETAILS - MILK RECORDS STREAM: Received $milkRecords.length milk records');
      _handleMilkRecordsUpdate(milkRecords);
    }, onError: (error) {
      debugPrint('❌ Cattle details - milk records stream error: $error');
    });

    // Health records stream
    _healthRecordsStreamSubscription = _isar.healthRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((healthRecords) {
      debugPrint('🔄 CATTLE DETAILS - HEALTH RECORDS STREAM: Received $healthRecords.length health records');
      _handleHealthRecordsUpdate(healthRecords);
    }, onError: (error) {
      debugPrint('❌ Cattle details - health records stream error: $error');
    });

    // Breeding records stream
    _breedingRecordsStreamSubscription = _isar.breedingRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((breedingRecords) {
      debugPrint('🔄 CATTLE DETAILS - BREEDING RECORDS STREAM: Received $breedingRecords.length breeding records');
      _handleBreedingRecordsUpdate(breedingRecords);
    }, onError: (error) {
      debugPrint('❌ Cattle details - breeding records stream error: $error');
    });

    debugPrint('✅ CATTLE DETAILS: Individual related record streams initialized');
  }

  /// Handle cattle data updates and load related data
  void _handleCattleUpdate(CattleIsar? cattle) async {
    try {
      _cattle = cattle;

      if (_cattle == null) {
        _setState(ControllerState.empty);
        return;
      }

      // Load family relationships
      await _loadFamilyRelationships();

      // Load related records for analytics
      await _loadRelatedRecords(_cattle!);

      // Load breeds specific to this cattle's animal type if needed
      if (_cattle!.animalTypeId != null && _cattle!.animalTypeId!.isNotEmpty) {
        await _loadBreedsForAnimalType(_cattle!.animalTypeId!);
      }

      // Calculate individual analytics
      await _calculateIndividualAnalytics();

      // Initialize related records streams after cattle is loaded
      if (_milkRecordsStreamSubscription == null) {
        _initializeRelatedRecordsStreams();
      }

      _setState(ControllerState.loaded);
    } catch (e) {
      debugPrint('Error handling cattle update: $e');
      _setError('Failed to update cattle data: $e');
    }
  }

  /// Handle real-time updates from milk records stream
  /// Following the breeding details pattern for individual stream handling
  void _handleMilkRecordsUpdate(List<MilkRecordIsar> allMilkRecords) {
    if (_cattle?.businessId == null) return;

    // Filter records for this specific cattle
    final cattleBusinessId = _cattle!.businessId!;
    final filteredMilkRecords = allMilkRecords
        .where((record) => record.cattleBusinessId == cattleBusinessId)
        .toList();

    // Update local data if there are changes
    if (filteredMilkRecords.length != _milkRecords.length) {
      _milkRecords = filteredMilkRecords;
      _calculateIndividualAnalytics();
      notifyListeners();
    }
  }

  /// Handle real-time updates from health records stream
  /// Following the breeding details pattern for individual stream handling
  void _handleHealthRecordsUpdate(List<HealthRecordIsar> allHealthRecords) {
    if (_cattle?.businessId == null) return;

    // Filter records for this specific cattle
    final cattleBusinessId = _cattle!.businessId!;
    final filteredHealthRecords = allHealthRecords
        .where((record) => record.cattleBusinessId == cattleBusinessId)
        .toList();

    // Update local data if there are changes
    if (filteredHealthRecords.length != _healthRecords.length) {
      _healthRecords = filteredHealthRecords;
      _calculateIndividualAnalytics();
      notifyListeners();
    }
  }

  /// Handle real-time updates from breeding records stream
  /// Following the breeding details pattern for individual stream handling
  void _handleBreedingRecordsUpdate(List<BreedingRecordIsar> allBreedingRecords) {
    if (_cattle?.businessId == null) return;

    // Filter records for this specific cattle
    final cattleBusinessId = _cattle!.businessId!;
    final filteredBreedingRecords = allBreedingRecords
        .where((record) => record.cattleId == cattleBusinessId)
        .toList();

    // Update local data if there are changes
    if (filteredBreedingRecords.length != _breedingRecords.length) {
      _breedingRecords = filteredBreedingRecords;
      _calculateIndividualAnalytics();
      notifyListeners();
    }
  }

  /// Load family relationships (mother, offspring, siblings)
  Future<void> _loadFamilyRelationships() async {
    if (_cattle == null) return;

    try {
      // Get all cattle from the database and filter locally
      // This follows the repository pattern where repositories only provide streams
      final allCattle = await _isar.cattleIsars.where().findAll();

      // Find mother by tag ID
      _motherCattle = _cattle!.motherTagId?.isNotEmpty == true
          ? allCattle.firstWhere(
              (c) => c.tagId == _cattle!.motherTagId,
              orElse: () => CattleIsar(),
            )
          : null;

      // If mother not found, set to null
      if (_motherCattle?.tagId == null) _motherCattle = null;

      // Find offspring (calves) - cattle that have this cattle as mother
      _offspring = _cattle!.tagId?.isNotEmpty == true
          ? allCattle
              .where((c) => c.motherTagId == _cattle!.tagId)
              .toList()
          : [];

      // Find siblings - cattle with same mother, excluding self
      _siblings = _cattle!.motherTagId?.isNotEmpty == true
          ? allCattle
              .where((c) =>
                  c.motherTagId == _cattle!.motherTagId &&
                  c.businessId != _cattle!.businessId)
              .toList()
          : [];

      debugPrint('DEBUG: Loaded family relationships:');
      debugPrint('  Mother: ${_motherCattle?.name}');
      debugPrint('  Offspring: $_offspring.length');
      debugPrint('  Siblings: $_siblings.length');
    } catch (e) {
      debugPrint('Error loading family relationships: $e');
      // Don't fail the entire load for relationship errors
    }
  }

  Future<void> _loadAnimalTypes() async {
    try {
      final animalTypesData = await _farmSetupRepository.getAllAnimalTypes();
      _animalTypes = animalTypesData;
      // Create lookup map for O(1) access
      _animalTypeMap = {
        for (var type in _animalTypes)
          if (type.businessId != null) type.businessId!: type
      };
    } catch (e) {
      debugPrint('Error loading animal types: $e');
      throw Exception('Failed to load animal types');
    }
  }

  Future<void> _loadBreeds() async {
    try {
      final breedsData = await _farmSetupRepository.getAllBreedCategories();
      _breeds = breedsData;
      // Create lookup map for O(1) access
      _breedMap = {
        for (var breed in _breeds)
          if (breed.businessId != null) breed.businessId!: breed
      };
    } catch (e) {
      debugPrint('Error loading breeds: $e');
      throw Exception('Failed to load breeds');
    }
  }



  /// Load breeds specific to an animal type
  Future<void> _loadBreedsForAnimalType(String animalTypeId) async {
    try {
      final specificBreeds = await _farmSetupRepository
          .getBreedCategoriesForAnimalType(animalTypeId);

      if (specificBreeds.isNotEmpty) {
        _breeds = specificBreeds;
        // Update lookup map
        _breedMap = {
          for (var breed in _breeds)
            if (breed.businessId != null) breed.businessId!: breed
        };
        debugPrint('DEBUG: Loaded $_breeds.length specific breeds for animal type');
      }
    } catch (e) {
      debugPrint('Error loading breeds for animal type $animalTypeId: $e');
      // Keep existing breeds on error
    }
  }

  /// Refresh all data
  Future<void> refresh() async {
    try {
      await Future.wait([
        _loadAnimalTypes(),
        _loadBreeds(),
      ]);

      // Reload family relationships if we have cattle
      if (_cattle != null) {
        await _loadFamilyRelationships();
      }

      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing cattle details data: $e\n$stackTrace');
      throw Exception('Failed to refresh cattle details: ${e.toString()}');
    }
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates

  /// Update cattle - only updates database, stream handles UI update
  Future<void> updateCattle(CattleIsar updatedCattle) async {
    try {
      await _cattleRepository.saveCattle(updatedCattle);

      // Check if relationships need to be reloaded
      final tagIdChanged = updatedCattle.tagId != _cattle?.tagId;
      final motherTagIdChanged = updatedCattle.motherTagId != _cattle?.motherTagId;

      if (tagIdChanged || motherTagIdChanged) {
        // Reload family relationships after tag changes
        await _loadFamilyRelationships();
      }

      // Stream will handle the main cattle update automatically
    } catch (e) {
      debugPrint('Error updating cattle: $e');
      throw Exception('Failed to update cattle: ${_getFriendlyErrorMessage(e)}');
    }
  }

  /// Delete cattle - only updates database, stream handles UI update
  Future<void> deleteCattle() async {
    if (_cattle?.businessId == null) {
      throw Exception('No cattle to delete');
    }

    try {
      await _cattleRepository.deleteCattle(_cattle!.id);
      // Stream will handle the UI update automatically
    } catch (e) {
      debugPrint('Error deleting cattle: $e');
      throw Exception('Failed to delete cattle: ${_getFriendlyErrorMessage(e)}');
    }
  }

  /// Helper method to convert exceptions to user-friendly messages
  String _getFriendlyErrorMessage(Object e) {
    // Provide specific error messages based on exception type
    if (e.toString().contains('ValidationException')) {
      return e.toString().replaceAll('ValidationException: ', '');
    } else if (e.toString().contains('RecordNotFoundException')) {
      return 'Cattle record not found. It may have been deleted.';
    } else if (e.toString().contains('DatabaseException')) {
      return 'Database error. Please try again later.';
    } else if (e.toString().contains('TimeoutException')) {
      return 'Operation timed out. Please check your connection and try again.';
    } else if (e.toString().contains('SocketException') ||
        e.toString().contains('Connection refused')) {
      return 'Network error. Please check your connection.';
    }

    // Default message for unknown errors
    return 'An unexpected error occurred. Please try again.';
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  /// Load related records for analytics calculation
  Future<void> _loadRelatedRecords(CattleIsar cattle) async {
    try {
      final cattleBusinessId = cattle.businessId;
      if (cattleBusinessId == null || cattleBusinessId.isEmpty) {
        debugPrint('No business ID available for loading related records');
        return;
      }

      // Load all related records by filtering from the database
      // Following the repository pattern where repositories only provide streams
      final results = await Future.wait([
        _isar.milkRecordIsars.filter().cattleBusinessIdEqualTo(cattleBusinessId).findAll(),
        _isar.healthRecordIsars.filter().cattleBusinessIdEqualTo(cattleBusinessId).findAll(),
        _isar.breedingRecordIsars.filter().cattleIdEqualTo(cattleBusinessId).findAll(),
      ]);

      _milkRecords = results[0] as List<MilkRecordIsar>;
      _healthRecords = results[1] as List<HealthRecordIsar>;
      _breedingRecords = results[2] as List<BreedingRecordIsar>;

      debugPrint('DEBUG: Loaded related records:');
      debugPrint('  Milk records: $_milkRecords.length');
      debugPrint('  Health records: $_healthRecords.length');
      debugPrint('  Breeding records: $_breedingRecords.length');
    } catch (e) {
      debugPrint('Error loading related records: $e');
      // Don't fail the entire load for related records errors
      _milkRecords = [];
      _healthRecords = [];
      _breedingRecords = [];
    }
  }

  /// Calculate individual analytics using the service
  Future<void> _calculateIndividualAnalytics() async {
    if (_cattle == null) {
      _individualAnalytics = IndividualCattleAnalyticsResult.empty;
      return;
    }

    try {
      // Get animal type for gestation period
      final animalType = await _farmSetupRepository.getAnimalTypeById(_cattle!.animalTypeId ?? '');

      // Create pricing settings (TODO: Get from user settings/preferences)
      final pricingSettings = FarmPricingSettings.defaults();

      _individualAnalytics = IndividualCattleAnalyticsService.calculate(
        _cattle!,
        _milkRecords,
        _healthRecords,
        _breedingRecords,
        animalType ?? AnimalTypeIsar(), // Fallback to empty animal type
        pricingSettings,
      );

      debugPrint('DEBUG: Calculated individual analytics:');
      debugPrint('  Lifetime milk yield: $_individualAnalytics.production.lifetimeMilkYieldL');
      debugPrint('  Average daily yield: $_individualAnalytics.production.averageDailyYieldL');
      debugPrint('  Total milking days: $_individualAnalytics.production.totalMilkingDays');
      debugPrint('DEBUG: Financial analytics:');
      debugPrint('  Total investment: $_individualAnalytics.financial.formattedTotalInvestment');
      debugPrint('  Current value: $_individualAnalytics.financial.formattedCurrentValue');
      debugPrint('  Total revenue: $_individualAnalytics.financial.formattedTotalRevenue');
      debugPrint('  Net profit: $_individualAnalytics.financial.formattedNetProfit');
      debugPrint('  Purchase price: ${_cattle?.formattedPurchasePrice ?? "N/A"}');
    } catch (e) {
      debugPrint('Error calculating individual analytics: $e');
      _individualAnalytics = IndividualCattleAnalyticsResult.empty;
    }
  }

  @override
  void dispose() {
    // Cancel all individual stream subscriptions - following breeding details pattern
    _cattleStreamSubscription?.cancel();
    _milkRecordsStreamSubscription?.cancel();
    _healthRecordsStreamSubscription?.cancel();
    _breedingRecordsStreamSubscription?.cancel();
    super.dispose();
  }
}
