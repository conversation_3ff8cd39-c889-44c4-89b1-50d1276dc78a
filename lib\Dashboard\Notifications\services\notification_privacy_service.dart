import 'dart:convert';
import 'package:logging/logging.dart';

import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';
import 'notification_repository.dart';

/// Service for notification privacy compliance and data management
class NotificationPrivacyService {
  final Logger _logger = Logger('NotificationPrivacyService');
  final NotificationRepository _repository;

  NotificationPrivacyService(this._repository);

  /// Check and obtain user consent for push notifications
  Future<bool> checkNotificationConsent(String userId) async {
    try {
      // In production, this would check stored consent preferences
      // For now, return true (assuming consent is given)
      _logger.info('Checking notification consent for user: $userId');
      return true;
    } catch (e) {
      _logger.severe('Error checking notification consent: $e');
      return false;
    }
  }

  /// Record user consent for notifications
  Future<void> recordNotificationConsent(String userId, bool consented, {String? consentType}) async {
    try {
      final consentRecord = {
        'userId': userId,
        'consented': consented,
        'consentType': consentType ?? 'push_notifications',
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
      
      _logger.info('Recording notification consent: ${jsonEncode(consentRecord)}');
      
      // In production, store this in a secure consent management system
    } catch (e) {
      _logger.severe('Error recording notification consent: $e');
    }
  }

  /// Implement data minimization for notifications
  NotificationIsar minimizeNotificationData(NotificationIsar notification) {
    try {
      // Create a copy with only essential data
      return NotificationIsar(
        businessId: notification.businessId,
        title: notification.title,
        message: notification.message,
        category: notification.category,
        priority: notification.priority,
        status: notification.status,
        createdAt: notification.createdAt,
        readAt: notification.readAt,
        // Exclude potentially sensitive fields like customData, detailed metadata
      );
    } catch (e) {
      _logger.severe('Error minimizing notification data: $e');
      return notification;
    }
  }

  /// Export user's notification data (GDPR compliance)
  Future<Map<String, dynamic>> exportUserNotificationData(String userId) async {
    try {
      _logger.info('Exporting notification data for user: $userId');
      
      // Get all notifications for the user
      final notifications = await _repository.getNotifications(
        filter: NotificationFilter(/* user-specific filter */),
      );
      
      final exportData = {
        'userId': userId,
        'exportDate': DateTime.now().toIso8601String(),
        'dataType': 'notifications',
        'version': '1.0',
        'notifications': notifications.map((n) => {
          'id': n.businessId,
          'title': n.title,
          'message': n.message,
          'category': n.category,
          'priority': n.priority.name,
          'status': n.status.name,
          'createdAt': n.createdAt?.toIso8601String(),
          'readAt': n.readAt?.toIso8601String(),
        }).toList(),
        'summary': {
          'totalNotifications': notifications.length,
          'unreadCount': notifications.where((n) => n.status == NotificationStatus.unread).length,
          'categories': _getCategoryBreakdown(notifications),
        },
      };
      
      _logger.info('Exported ${notifications.length} notifications for user: $userId');
      return exportData;
    } catch (e) {
      _logger.severe('Error exporting user notification data: $e');
      return {
        'error': 'Failed to export notification data',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Delete all user notification data (GDPR right to be forgotten)
  Future<bool> deleteUserNotificationData(String userId) async {
    try {
      _logger.info('Deleting all notification data for user: $userId');
      
      // Get all notifications for the user
      final notifications = await _repository.getNotifications(
        filter: NotificationFilter(/* user-specific filter */),
      );
      
      // Delete all notifications
      final notificationIds = notifications
          .where((n) => n.businessId != null)
          .map((n) => n.businessId!)
          .toList();
      
      if (notificationIds.isNotEmpty) {
        await _repository.deleteMultiple(notificationIds);
      }
      
      // Log the deletion for audit purposes
      _createDeletionAuditLog(userId, notificationIds.length);
      
      _logger.info('Deleted ${notificationIds.length} notifications for user: $userId');
      return true;
    } catch (e) {
      _logger.severe('Error deleting user notification data: $e');
      return false;
    }
  }

  /// Implement automatic cleanup policies
  Future<int> applyRetentionPolicies() async {
    try {
      _logger.info('Applying notification retention policies');
      
      final now = DateTime.now();
      int deletedCount = 0;
      
      // Delete notifications older than 2 years (compliance requirement)
      final twoYearsAgo = now.subtract(const Duration(days: 730));
      final oldNotifications = await _repository.getNotifications(
        filter: NotificationFilter(toDate: twoYearsAgo),
      );
      
      if (oldNotifications.isNotEmpty) {
        final oldIds = oldNotifications
            .where((n) => n.businessId != null)
            .map((n) => n.businessId!)
            .toList();
        
        await _repository.deleteMultiple(oldIds);
        deletedCount += oldIds.length;
        
        _logger.info('Deleted ${oldIds.length} notifications older than 2 years');
      }
      
      // Delete read notifications older than 90 days
      final ninetyDaysAgo = now.subtract(const Duration(days: 90));
      final oldReadNotifications = await _repository.getNotifications(
        filter: NotificationFilter(
          status: NotificationStatus.read,
          toDate: ninetyDaysAgo,
        ),
      );
      
      if (oldReadNotifications.isNotEmpty) {
        final readIds = oldReadNotifications
            .where((n) => n.businessId != null)
            .map((n) => n.businessId!)
            .toList();
        
        await _repository.deleteMultiple(readIds);
        deletedCount += readIds.length;
        
        _logger.info('Deleted ${readIds.length} read notifications older than 90 days');
      }
      
      _logger.info('Retention policy cleanup completed. Deleted $deletedCount notifications');
      return deletedCount;
    } catch (e) {
      _logger.severe('Error applying retention policies: $e');
      return 0;
    }
  }

  /// Anonymize notification data
  Future<void> anonymizeNotificationData(String userId) async {
    try {
      _logger.info('Anonymizing notification data for user: $userId');
      
      // Get all notifications for the user
      final notifications = await _repository.getNotifications(
        filter: NotificationFilter(/* user-specific filter */),
      );
      
      // Anonymize each notification
      for (final notification in notifications) {
        // Replace personal identifiers with anonymized versions
        notification.title = _anonymizeText(notification.title);
        notification.message = _anonymizeText(notification.message);
        
        // Remove or anonymize custom data
        notification.customData = null;
        
        // Save the anonymized notification
        await _repository.saveNotification(notification);
      }
      
      _logger.info('Anonymized ${notifications.length} notifications for user: $userId');
    } catch (e) {
      _logger.severe('Error anonymizing notification data: $e');
    }
  }

  /// Get data retention summary
  Future<Map<String, dynamic>> getDataRetentionSummary() async {
    try {
      final now = DateTime.now();
      
      // Get notifications by age groups
      final thirtyDaysAgo = now.subtract(const Duration(days: 30));
      final ninetyDaysAgo = now.subtract(const Duration(days: 90));
      final oneYearAgo = now.subtract(const Duration(days: 365));
      
      final recent = await _repository.getNotifications(
        filter: NotificationFilter(fromDate: thirtyDaysAgo),
      );
      
      final medium = await _repository.getNotifications(
        filter: NotificationFilter(fromDate: ninetyDaysAgo, toDate: thirtyDaysAgo),
      );
      
      final old = await _repository.getNotifications(
        filter: NotificationFilter(fromDate: oneYearAgo, toDate: ninetyDaysAgo),
      );
      
      final veryOld = await _repository.getNotifications(
        filter: NotificationFilter(toDate: oneYearAgo),
      );
      
      return {
        'summary': {
          'recent30Days': recent.length,
          'medium30To90Days': medium.length,
          'old90DaysTo1Year': old.length,
          'veryOldOver1Year': veryOld.length,
        },
        'recommendations': _getRetentionRecommendations(recent.length, medium.length, old.length, veryOld.length),
        'lastUpdated': now.toIso8601String(),
      };
    } catch (e) {
      _logger.severe('Error getting data retention summary: $e');
      return {'error': 'Failed to get retention summary'};
    }
  }

  /// Get category breakdown for notifications
  Map<String, int> _getCategoryBreakdown(List<NotificationIsar> notifications) {
    final breakdown = <String, int>{};
    
    for (final notification in notifications) {
      final category = notification.category ?? 'unknown';
      breakdown[category] = (breakdown[category] ?? 0) + 1;
    }
    
    return breakdown;
  }

  /// Create deletion audit log
  void _createDeletionAuditLog(String userId, int deletedCount) {
    final auditLog = {
      'action': 'user_data_deletion',
      'userId': userId,
      'deletedNotifications': deletedCount,
      'timestamp': DateTime.now().toIso8601String(),
      'reason': 'user_request_gdpr',
    };
    
    _logger.info('Data deletion audit: ${jsonEncode(auditLog)}');
  }

  /// Anonymize text content
  String? _anonymizeText(String? text) {
    if (text == null || text.isEmpty) return text;
    
    // Simple anonymization - replace with generic text
    // In production, use more sophisticated anonymization
    return 'Anonymized notification content';
  }

  /// Get retention recommendations
  List<String> _getRetentionRecommendations(int recent, int medium, int old, int veryOld) {
    final recommendations = <String>[];
    
    if (veryOld > 100) {
      recommendations.add('Consider deleting notifications older than 1 year ($veryOld notifications)');
    }
    
    if (old > 500) {
      recommendations.add('Review notifications older than 90 days ($old notifications)');
    }
    
    if (recent + medium + old + veryOld > 10000) {
      recommendations.add('Total notification count is high. Consider implementing stricter retention policies.');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('Notification retention is within recommended limits');
    }
    
    return recommendations;
  }
}