import 'package:get_it/get_it.dart';
import 'package:logging/logging.dart';

import '../models/event_isar.dart';
import '../models/event_enums.dart';
import 'events_repository.dart';
import 'event_notification_service.dart';

/// Manager service that coordinates event operations with notification management
/// This service acts as a higher-level coordinator to avoid circular dependencies
class EventNotificationManager {
  static final Logger _logger = Logger('EventNotificationManager');
  
  final EventsRepository _eventsRepository;
  final EventNotificationService _notificationService;

  /// Constructor with dependency injection
  EventNotificationManager({
    EventsRepository? eventsRepository,
    EventNotificationService? notificationService,
  }) : _eventsRepository = eventsRepository ?? GetIt.instance<EventsRepository>(),
       _notificationService = notificationService ?? EventNotificationService();

  /// Save an event and manage its notifications
  Future<void> saveEventWithNotifications(EventIsar event) async {
    try {
      // Check if this is an update to an existing event
      EventIsar? existingEvent;
      if (event.businessId != null) {
        existingEvent = await _eventsRepository.getEventByBusinessId(event.businessId!);
      }

      // Save the event first
      await _eventsRepository.saveEvent(event);

      // Handle notifications based on event status changes
      await _manageEventNotifications(event, existingEvent);
      
      _logger.info('Successfully saved event ${event.businessId} with notification management');
    } catch (e) {
      _logger.severe('Error saving event with notifications: $e');
      rethrow;
    }
  }

  /// Delete an event and cancel its notifications
  Future<void> deleteEventWithNotifications(String businessId) async {
    try {
      // Cancel notifications first
      await _notificationService.cancelEventReminders(businessId);
      
      // Then delete the event
      await _eventsRepository.deleteEventByBusinessId(businessId);
      
      _logger.info('Successfully deleted event $businessId with notification cleanup');
    } catch (e) {
      _logger.severe('Error deleting event with notifications: $e');
      rethrow;
    }
  }

  /// Complete an event and cancel its notifications
  Future<void> completeEventWithNotifications(EventIsar event, {
    String? completedBy,
    String? completionNotes,
    double? actualCost,
  }) async {
    try {
      // Mark event as completed
      event.markCompleted(
        completedBy: completedBy,
        completionNotes: completionNotes,
        actualCost: actualCost,
      );

      // Save the updated event
      await _eventsRepository.saveEvent(event);

      // Cancel any pending notifications
      await _notificationService.cancelEventReminders(event.businessId ?? '');
      
      _logger.info('Successfully completed event ${event.businessId} with notification cleanup');
    } catch (e) {
      _logger.severe('Error completing event with notifications: $e');
      rethrow;
    }
  }

  /// Reschedule an event and update its notifications
  Future<void> rescheduleEventWithNotifications(EventIsar event, DateTime newScheduledDate) async {
    try {
      final oldDate = event.scheduledDate;
      
      // Update the event's scheduled date
      event.scheduledDate = newScheduledDate;
      event.updatedAt = DateTime.now();

      // Save the updated event
      await _eventsRepository.saveEvent(event);

      // If the date changed, update notifications
      if (oldDate != newScheduledDate) {
        await _notificationService.cancelEventReminders(event.businessId ?? '');
        
        // Only reschedule if the event is still active
        if (event.status == EventStatus.scheduled || event.status == EventStatus.inProgress) {
          await _notificationService.scheduleEventReminders(event);
        }
      }
      
      _logger.info('Successfully rescheduled event ${event.businessId} from $oldDate to $newScheduledDate');
    } catch (e) {
      _logger.severe('Error rescheduling event with notifications: $e');
      rethrow;
    }
  }

  /// Process background notifications for all events
  Future<void> processBackgroundNotifications() async {
    try {
      await _notificationService.sendOverdueNotifications();
      await _notificationService.sendUpcomingEventNotifications();
      _logger.info('Successfully processed background notifications');
    } catch (e) {
      _logger.severe('Error processing background notifications: $e');
    }
  }

  /// Initialize the notification service
  void initialize() {
    _notificationService.initialize();
  }

  /// Dispose of resources
  void dispose() {
    _notificationService.dispose();
  }

  /// Private method to manage event notifications based on status changes
  Future<void> _manageEventNotifications(EventIsar event, EventIsar? existingEvent) async {
    try {
      if (event.status == EventStatus.completed || event.status == EventStatus.cancelled) {
        // Cancel notifications for completed/cancelled events
        await _notificationService.cancelEventReminders(event.businessId ?? '');
        _logger.info('Cancelled notifications for ${event.status.displayName.toLowerCase()} event ${event.businessId}');
      } else if (event.status == EventStatus.scheduled || event.status == EventStatus.inProgress) {
        // Schedule notifications for new or rescheduled events
        final isNewEvent = existingEvent == null;
        final dateChanged = existingEvent?.scheduledDate != event.scheduledDate;
        final statusChanged = existingEvent?.status != event.status;
        
        if (isNewEvent || dateChanged || statusChanged) {
          // Cancel existing notifications first
          await _notificationService.cancelEventReminders(event.businessId ?? '');
          
          // Schedule new notifications
          await _notificationService.scheduleEventReminders(event);
          
          if (isNewEvent) {
            _logger.info('Scheduled notifications for new event ${event.businessId}');
          } else if (dateChanged) {
            _logger.info('Rescheduled notifications for event ${event.businessId} due to date change');
          } else if (statusChanged) {
            _logger.info('Updated notifications for event ${event.businessId} due to status change');
          }
        }
      }
    } catch (e) {
      _logger.warning('Failed to manage notifications for event ${event.businessId}: $e');
      // Don't fail the save operation if notification management fails
    }
  }
}