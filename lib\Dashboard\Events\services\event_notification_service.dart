import 'dart:async';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'package:logging/logging.dart';

import '../models/event_isar.dart';
import '../models/event_enums.dart';
import '../../Notifications/models/notification_isar.dart';
import '../../Notifications/models/notification_priority.dart';
import '../../Notifications/models/notification_settings_isar.dart';
import '../../Notifications/services/notifications_repository.dart';
import '../../../services/database/isar_service.dart';

/// Service for managing event notifications and reminders
/// 
/// This service handles:
/// - Scheduling event reminders based on event dates
/// - Sending overdue event notifications
/// - Managing upcoming event notifications
/// - Cancelling notifications when events are completed
/// - Respecting user notification preferences
class EventNotificationService {
  static final Logger _logger = Logger('EventNotificationService');
  
  final NotificationsRepository _notificationsRepository;
  final IsarService _isarService;

  // Timer for background processing
  Timer? _backgroundTimer;
  static const Duration _backgroundInterval = Duration(minutes: 15);

  /// Constructor with dependency injection
  EventNotificationService({
    NotificationsRepository? notificationsRepository,
    IsarService? isarService,
  }) : _notificationsRepository = notificationsRepository ?? GetIt.instance<NotificationsRepository>(),
       _isarService = isarService ?? GetIt.instance<IsarService>();

  /// Getter for Isar instance
  Isar get _isar => _isarService.isar;

  /// Initialize the notification service and start background processing
  void initialize() {
    _startBackgroundProcessing();
  }

  /// Dispose of resources
  void dispose() {
    _backgroundTimer?.cancel();
  }

  /// Schedule reminders for an event
  Future<void> scheduleEventReminders(EventIsar event) async {
    try {
      // Check if notifications are enabled for events
      final notificationSettings = await _getNotificationSettings();
      final categoryEnabled = notificationSettings?.getCategoryEnabled() ?? {};
      if (categoryEnabled['events'] != true) {
        _logger.info('Event notifications are disabled, skipping reminder scheduling');
        return;
      }

      // Don't schedule reminders for completed or cancelled events
      if (event.status == EventStatus.completed || event.status == EventStatus.cancelled) {
        _logger.info('Event ${event.businessId} is completed/cancelled, skipping reminder scheduling');
        return;
      }

      // Check if event has a scheduled date
      if (event.scheduledDate == null) {
        _logger.warning('Event ${event.businessId} has no scheduled date, cannot schedule reminders');
        return;
      }

      // Don't schedule reminders for events that are already overdue
      if (event.scheduledDate!.isBefore(DateTime.now())) {
        _logger.info('Event ${event.businessId} is already overdue, skipping reminder scheduling');
        return;
      }

      // Get reminder intervals from event or use defaults
      final reminderMinutes = event.reminderMinutes ?? _getDefaultReminderMinutes(event.priority);

      for (final minutes in reminderMinutes) {
        await _scheduleReminder(event, minutes);
      }
      
      _logger.info('Successfully scheduled ${reminderMinutes.length} reminders for event ${event.businessId}');
    } catch (e) {
      _logger.severe('Error scheduling event reminders for ${event.businessId}: $e');
    }
  }

  /// Cancel all reminders for an event
  Future<void> cancelEventReminders(String eventBusinessId) async {
    try {
      if (eventBusinessId.isEmpty) {
        _logger.warning('Cannot cancel reminders: empty event business ID');
        return;
      }

      // Find and delete all notifications for this event
      final notifications = await _isar.notificationIsars
          .filter()
          .relatedRecordIdEqualTo(eventBusinessId)
          .and()
          .group((q) => q
              .typeEqualTo('event_reminder')
              .or()
              .typeEqualTo('event_overdue')
              .or()
              .typeEqualTo('event_upcoming'))
          .findAll();

      for (final notification in notifications) {
        if (notification.businessId != null) {
          await _notificationsRepository.deleteNotification(notification.businessId!);
        }
      }
      
      _logger.info('Successfully cancelled ${notifications.length} notifications for event $eventBusinessId');
    } catch (e) {
      _logger.severe('Error cancelling event reminders for $eventBusinessId: $e');
    }
  }

  /// Send notifications for overdue events
  Future<void> sendOverdueNotifications() async {
    try {
      final notificationSettings = await _getNotificationSettings();
      final categoryEnabled = notificationSettings?.getCategoryEnabled() ?? {};
      if (categoryEnabled['events'] != true) {
        _logger.info('Event notifications are disabled, skipping overdue notifications');
        return;
      }

      final now = DateTime.now();
      
      // Find overdue events that are still scheduled
      final overdueEvents = await _isar.eventIsars
          .filter()
          .statusEqualTo(EventStatus.scheduled)
          .and()
          .scheduledDateLessThan(now)
          .findAll();

      _logger.info('Found ${overdueEvents.length} overdue events');

      for (final event in overdueEvents) {
        await _sendOverdueNotification(event);
        
        // Update event status to overdue
        event.status = EventStatus.overdue;
        event.updatedAt = DateTime.now();
        
        // Save the event directly to avoid circular dependency
        await _isar.writeTxn(() async {
          await _isar.eventIsars.put(event);
        });
      }
      
      _logger.info('Successfully processed ${overdueEvents.length} overdue events');
    } catch (e) {
      _logger.severe('Error sending overdue notifications: $e');
    }
  }

  /// Send notifications for upcoming events
  Future<void> sendUpcomingEventNotifications() async {
    try {
      final notificationSettings = await _getNotificationSettings();
      final categoryEnabled = notificationSettings?.getCategoryEnabled() ?? {};
      if (categoryEnabled['events'] != true) {
        _logger.info('Event notifications are disabled, skipping upcoming notifications');
        return;
      }

      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));
      
      // Find events due in the next 24 hours
      final upcomingEvents = await _isar.eventIsars
          .filter()
          .statusEqualTo(EventStatus.scheduled)
          .and()
          .scheduledDateBetween(now, tomorrow)
          .findAll();

      _logger.info('Found ${upcomingEvents.length} upcoming events');

      for (final event in upcomingEvents) {
        await _sendUpcomingEventNotification(event);
      }
      
      _logger.info('Successfully processed ${upcomingEvents.length} upcoming events');
    } catch (e) {
      _logger.severe('Error sending upcoming event notifications: $e');
    }
  }

  /// Schedule a specific reminder for an event
  Future<void> _scheduleReminder(EventIsar event, int minutesBefore) async {
    try {
      if (event.scheduledDate == null) {
        _logger.warning('Cannot schedule reminder: event has no scheduled date');
        return;
      }

      final reminderTime = event.scheduledDate!.subtract(Duration(minutes: minutesBefore));
      
      // Don't schedule reminders for past times
      if (reminderTime.isBefore(DateTime.now())) {
        _logger.info('Skipping reminder for event ${event.businessId}: reminder time is in the past');
        return;
      }

      // Check if we already have a reminder scheduled for this time
      final existingReminder = await _isar.notificationIsars
          .filter()
          .relatedRecordIdEqualTo(event.businessId ?? '')
          .and()
          .typeEqualTo('event_reminder')
          .and()
          .messageContains(_formatTimeUntil(minutesBefore))
          .findFirst();

      if (existingReminder != null) {
        _logger.info('Reminder already exists for event ${event.businessId} at ${_formatTimeUntil(minutesBefore)}');
        return;
      }

      final notification = NotificationIsar(
        businessId: const Uuid().v4(),
        type: 'event_reminder',
        title: _getReminderTitle(event, minutesBefore),
        message: _getReminderMessage(event, minutesBefore),
        cattleId: event.cattleTagId,
        relatedRecordId: event.businessId,
        createdAt: DateTime.now(),
        priority: _getPriorityEnum(event.priority),
      );

      await _notificationsRepository.addNotification(notification);
      _logger.info('Scheduled reminder for event ${event.businessId} at ${_formatTimeUntil(minutesBefore)} before');
    } catch (e) {
      _logger.severe('Error scheduling reminder for event ${event.businessId}: $e');
    }
  }

  /// Send overdue notification for an event
  Future<void> _sendOverdueNotification(EventIsar event) async {
    try {
      // Check if we already sent an overdue notification for this event
      final existingNotification = await _isar.notificationIsars
          .filter()
          .relatedRecordIdEqualTo(event.businessId ?? '')
          .and()
          .typeEqualTo('event_overdue')
          .findFirst();

      if (existingNotification != null) {
        _logger.info('Overdue notification already sent for event ${event.businessId}');
        return; // Already sent
      }

      if (event.scheduledDate == null) {
        _logger.warning('Cannot send overdue notification: event has no scheduled date');
        return;
      }

      final daysOverdue = DateTime.now().difference(event.scheduledDate!).inDays;
      
      final notification = NotificationIsar(
        businessId: const Uuid().v4(),
        type: 'event_overdue',
        title: 'Overdue Event: ${event.title ?? 'Unnamed Event'}',
        message: _getOverdueMessage(event, daysOverdue),
        cattleId: event.cattleTagId,
        relatedRecordId: event.businessId,
        createdAt: DateTime.now(),
        priority: NotificationPriority.high,
      );

      await _notificationsRepository.addNotification(notification);
      _logger.info('Sent overdue notification for event ${event.businessId}');
    } catch (e) {
      _logger.severe('Error sending overdue notification for event ${event.businessId}: $e');
    }
  }

  /// Send upcoming event notification
  Future<void> _sendUpcomingEventNotification(EventIsar event) async {
    try {
      // Check if we already sent an upcoming notification for this event today
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      
      final existingNotification = await _isar.notificationIsars
          .filter()
          .relatedRecordIdEqualTo(event.businessId ?? '')
          .and()
          .typeEqualTo('event_upcoming')
          .and()
          .createdAtGreaterThan(startOfDay)
          .findFirst();

      if (existingNotification != null) {
        _logger.info('Upcoming notification already sent today for event ${event.businessId}');
        return; // Already sent today
      }

      if (event.scheduledDate == null) {
        _logger.warning('Cannot send upcoming notification: event has no scheduled date');
        return;
      }

      final hoursUntil = event.scheduledDate!.difference(DateTime.now()).inHours;
      
      final notification = NotificationIsar(
        businessId: const Uuid().v4(),
        type: 'event_upcoming',
        title: 'Upcoming Event: ${event.title ?? 'Unnamed Event'}',
        message: _getUpcomingMessage(event, hoursUntil),
        cattleId: event.cattleTagId,
        relatedRecordId: event.businessId,
        createdAt: DateTime.now(),
        priority: _getPriorityEnum(event.priority),
      );

      await _notificationsRepository.addNotification(notification);
      _logger.info('Sent upcoming notification for event ${event.businessId}');
    } catch (e) {
      _logger.severe('Error sending upcoming notification for event ${event.businessId}: $e');
    }
  }

  /// Get notification settings
  Future<NotificationSettingsIsar?> _getNotificationSettings() async {
    try {
      return await _notificationsRepository.getNotificationSettings();
    } catch (e) {
      _logger.warning('Failed to get notification settings: $e');
      return null;
    }
  }

  /// Get default reminder minutes based on event priority
  List<int> _getDefaultReminderMinutes(EventPriority priority) {
    switch (priority) {
      case EventPriority.critical:
        return [10080, 1440, 60]; // 1 week, 1 day, 1 hour
      case EventPriority.high:
        return [1440, 60]; // 1 day, 1 hour
      case EventPriority.medium:
        return [1440]; // 1 day
      case EventPriority.low:
        return [1440]; // 1 day
    }
  }

  /// Generate reminder title
  String _getReminderTitle(EventIsar event, int minutesBefore) {
    final timeText = _formatTimeUntil(minutesBefore);
    return 'Reminder: ${event.title} in $timeText';
  }

  /// Generate reminder message
  String _getReminderMessage(EventIsar event, int minutesBefore) {
    final timeText = _formatTimeUntil(minutesBefore);
    final parts = <String>[];
    
    parts.add('${event.title ?? 'Unnamed Event'} is scheduled in $timeText');
    
    if (event.cattleTagId?.isNotEmpty == true) {
      parts.add('Cattle: ${event.cattleTagId}');
    }
    
    if (event.description?.isNotEmpty == true) {
      parts.add('Details: ${event.description}');
    }
    
    return parts.join('\n');
  }

  /// Generate overdue message
  String _getOverdueMessage(EventIsar event, int daysOverdue) {
    final parts = <String>[];
    
    if (daysOverdue == 0) {
      parts.add('This event was due today');
    } else if (daysOverdue == 1) {
      parts.add('This event was due yesterday');
    } else {
      parts.add('This event was due $daysOverdue days ago');
    }
    
    if (event.cattleTagId?.isNotEmpty == true) {
      parts.add('Cattle: ${event.cattleTagId}');
    }
    
    parts.add('Please complete or reschedule this event');
    
    return parts.join('\n');
  }

  /// Generate upcoming message
  String _getUpcomingMessage(EventIsar event, int hoursUntil) {
    final parts = <String>[];
    
    if (hoursUntil <= 1) {
      parts.add('This event is due within the next hour');
    } else if (hoursUntil <= 24) {
      parts.add('This event is due in $hoursUntil hours');
    } else {
      final days = (hoursUntil / 24).round();
      parts.add('This event is due in $days day${days > 1 ? 's' : ''}');
    }
    
    if (event.cattleTagId?.isNotEmpty == true) {
      parts.add('Cattle: ${event.cattleTagId}');
    }
    
    return parts.join('\n');
  }

  /// Convert EventPriority to NotificationPriority enum
  NotificationPriority _getPriorityEnum(EventPriority priority) {
    switch (priority) {
      case EventPriority.low:
        return NotificationPriority.low;
      case EventPriority.medium:
        return NotificationPriority.medium;
      case EventPriority.high:
        return NotificationPriority.high;
      case EventPriority.critical:
        return NotificationPriority.critical;
    }
  }



  /// Format time until event
  String _formatTimeUntil(int minutes) {
    if (minutes < 60) {
      return '$minutes minute${minutes != 1 ? 's' : ''}';
    } else if (minutes < 1440) {
      final hours = (minutes / 60).round();
      return '$hours hour${hours != 1 ? 's' : ''}';
    } else {
      final days = (minutes / 1440).round();
      return '$days day${days != 1 ? 's' : ''}';
    }
  }

  /// Start background processing for notifications
  void _startBackgroundProcessing() {
    _backgroundTimer = Timer.periodic(_backgroundInterval, (timer) {
      _processBackgroundNotifications();
    });
  }

  /// Process background notifications
  Future<void> _processBackgroundNotifications() async {
    try {
      await sendOverdueNotifications();
      await sendUpcomingEventNotifications();
    } catch (e) {
      _logger.severe('Error processing background notifications: $e');
    }
  }
}
