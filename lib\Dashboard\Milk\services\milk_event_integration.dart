import '../models/milk_record_isar.dart';

class MilkEventIntegration {
  // EventService integration temporarily disabled
  // final EventService _eventService;

  MilkEventIntegration();

  /// Create milk-related events
  Future<void> createMilkEvents(MilkRecordIsar milkRecord) async {
    // EventService integration temporarily disabled
    /*
    try {
      // Create milking schedule event
      if (milkRecord.date != null) {
        await _createMilkingEvent(milkRecord);
      }

      // Create quality check event
      if (milkRecord.date != null) {
        await _createQualityCheckEvent(milkRecord);
      }

      // Create storage event
      if (milkRecord.date != null) {
        await _createStorageEvent(milkRecord);
      }

      // Create delivery event
      if (milkRecord.date != null) {
        await _createDeliveryEvent(milkRecord);
      }
    } catch (e) {
      debugPrint('Error creating milk events: $e');
    }
    */
  }

  /*
  /// Create milking event
  Future<void> _createMilkingEvent(MilkRecordIsar milkRecord) async {
    final event = EventIsar()
      ..title = 'Milking - ${milkRecord.cattleBusinessId}'
      ..description = 'Scheduled milking for cattle ${milkRecord.cattleBusinessId} - ${milkRecord.totalAmountL}'
      // .eventType = EventType.milk
      // ..priority = EventPriority.medium
      // ..status = EventStatus.pending
      // .startDateTime = milkRecord.date!
      // .endDateTime = milkRecord.date!.add(const Duration(hours: 1))
      // .cattleIds = [milkRecord.cattleBusinessId!]
      ..businessId = milkRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create quality check event
  Future<void> _createQualityCheckEvent(MilkRecordIsar milkRecord) async {
    final event = EventIsar()
      ..title = 'Milk Quality Check - ${milkRecord.cattleBusinessId}'
      ..description = 'Quality check for milk from cattle ${milkRecord.cattleBusinessId}'
      // .eventType = EventType.milk
      // ..priority = EventPriority.high
      // ..status = EventStatus.pending
      // .startDateTime = milkRecord.date!
      // .endDateTime = milkRecord.date!.add(const Duration(hours: 1))
      // .cattleIds = [milkRecord.cattleBusinessId!]
      ..businessId = milkRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create storage event
  Future<void> _createStorageEvent(MilkRecordIsar milkRecord) async {
    final event = EventIsar()
      ..title = 'Milk Storage - ${milkRecord.cattleBusinessId}'
      ..description = 'Store milk from cattle ${milkRecord.cattleBusinessId} - ${milkRecord.totalAmountL}'
      // .eventType = EventType.milk
      // ..priority = EventPriority.medium
      // ..status = EventStatus.pending
      // .startDateTime = milkRecord.date!
      // .endDateTime = milkRecord.date!.add(const Duration(hours: 1))
      // .cattleIds = [milkRecord.cattleBusinessId!]
      ..businessId = milkRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }

  /// Create delivery event
  Future<void> _createDeliveryEvent(MilkRecordIsar milkRecord) async {
    final event = EventIsar()
      ..title = 'Milk Delivery - ${milkRecord.cattleBusinessId}'
      ..description = 'Deliver milk from cattle ${milkRecord.cattleBusinessId} - ${milkRecord.totalAmountL}'
      // .eventType = EventType.milk
      // ..priority = EventPriority.high
      // ..status = EventStatus.pending
      // .startDateTime = milkRecord.date!
      // .endDateTime = milkRecord.date!.add(const Duration(hours: 2))
      // .cattleIds = [milkRecord.cattleBusinessId!]
      ..businessId = milkRecord.businessId!
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await _eventService.createEvent(event);
  }
  */

  /*
  /// Create daily milking schedule
  Future<void> createDailyMilkingSchedule({
    required String cattleId,
    required String businessId,
    required DateTime date,
    required List<String> milkingTimes,
  }) async {
    try {
      for (final time in milkingTimes) {
        final timeParts = time.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);

        final milkingTime = DateTime(
          date.year,
          date.month,
          date.day,
          hour,
          minute,
        );

        final event = EventIsar()
          ..title = 'Milking - $cattleId'
          ..description = 'Daily milking schedule for cattle $cattleId'
          // .eventType = EventType.milk
          // ..priority = EventPriority.medium
          // ..status = EventStatus.pending
          // .startDateTime = milkingTime
          // .endDateTime = milkingTime.add(const Duration(hours: 1))
          // .cattleIds = [cattleId]
          ..businessId = businessId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..isRecurring = true;
          // .recurrenceRule = 'FREQ=DAILY';

        await _eventService.createEvent(event);
      }
    } catch (e) {
      debugPrint('Error creating daily milking schedule: $e');
    }
  }
  */

  /*
  /// Update milk events
  Future<void> updateMilkEvents(MilkRecordIsar milkRecord) async {
    try {
      // Update existing events based on milk record changes
      final events = await _eventService.getEventsByCattleId(milkRecord.cattleBusinessId!);

      for (final event in events) {
        if (event.eventType == EventType.milk && event.businessId == milkRecord.businessId) {
          await _updateEventFromMilkRecord(event, milkRecord);
        }
      }
    } catch (e) {
      debugPrint('Error updating milk events: $e');
    }
  }

  /// Update event from milk record
  Future<void> _updateEventFromMilkRecord(
    EventIsar event,
    MilkRecordIsar milkRecord,
  ) async {
    if (event.title?.contains('Milking') == true && milkRecord.date != null) {
      event.startDateTime = milkRecord.date!;
      event.endDateTime = milkRecord.date!.add(const Duration(hours: 1));
      event.description = 'Scheduled milking for cattle ${milkRecord.cattleBusinessId} - ${milkRecord.totalAmount}L';
    } else if (event.title?.contains('Quality Check') == true && milkRecord.date != null) {
      event.startDateTime = milkRecord.date!;
      event.endDateTime = milkRecord.date!.add(const Duration(hours: 1));
    } else if (event.title?.contains('Storage') == true && milkRecord.date != null) {
      event.startDateTime = milkRecord.date!;
      event.endDateTime = milkRecord.date!.add(const Duration(hours: 1));
      event.description = 'Store milk from cattle ${milkRecord.cattleBusinessId} - ${milkRecord.totalAmount}L';
    } else if (event.title?.contains('Delivery') == true && milkRecord.date != null) {
      event.startDateTime = milkRecord.date!;
      event.endDateTime = milkRecord.date!.add(const Duration(hours: 2));
      event.description = 'Deliver milk from cattle ${milkRecord.cattleBusinessId} - ${milkRecord.totalAmount}L';
    }

    event.updatedAt = DateTime.now();
    await _eventService.updateEvent(event);
  }

  /// Delete milk events
  Future<void> deleteMilkEvents(String cattleId, String businessId) async {
    try {
      final events = await _eventService.getEventsByCattleId(cattleId);

      final milkEvents = events.where((event) =>
        event.eventType == EventType.milk && event.businessId == businessId
      ).toList();

      for (final event in milkEvents) {
        await _eventService.deleteEvent(event.id);
      }
    } catch (e) {
      debugPrint('Error deleting milk events: $e');
    }
  }

  /// Create milk production analysis event
  Future<void> createProductionAnalysisEvent({
    required String businessId,
    required DateTime analysisDate,
    required String analysisType,
  }) async {
    try {
      final event = EventIsar()
        ..title = 'Milk Production Analysis'
        ..description = 'Analyze milk production data for $analysisType'
        // .eventType = EventType.milk
        // ..priority = EventPriority.low
        // ..status = EventStatus.pending
        // .startDateTime = analysisDate
        // .endDateTime = analysisDate.add(const Duration(hours: 2))
        ..businessId = businessId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now()
        ..isRecurring = true;
        // .recurrenceRule = 'FREQ=WEEKLY';

      await _eventService.createEvent(event);
    } catch (e) {
      debugPrint('Error creating production analysis event: $e');
    }
  }
  */
}