import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';

import 'theme/app_theme.dart';
import 'services/logging_service.dart';
import 'services/auto_backup_service.dart';
import 'core/dependency_injection.dart';
import 'core/default_data_seeder.dart';
import 'Dashboard/Notifications/services/notification_service.dart';
import 'Dashboard/Notifications/services/notification_automation_service.dart';
import 'Dashboard/Notifications/services/event_notification_integration_service.dart';
import 'Dashboard/Notifications/services/notification_lifecycle_service.dart';

import 'routes/app_routes.dart';
import 'Dashboard/Cattle/screens/cattle_screen.dart';
import 'Dashboard/Cattle/controllers/cattle_controller.dart';
import 'Dashboard/Breeding/screens/breeding_screen.dart';
import 'Dashboard/Health/screens/health_screen.dart';
import 'Dashboard/Milk Records/screens/milk_screen.dart';
import 'Dashboard/Transactions/screens/transactions_screen.dart';
import 'Dashboard/Events/screens/events_screen.dart';
import 'Dashboard/Weight/screens/weight_screen.dart';
import 'Dashboard/Reports/screens/reports_screen.dart';

// Notification screens and controllers
import 'Dashboard/Notifications/screens/notifications_screen.dart';
import 'Dashboard/Notifications/screens/notification_center_screen.dart';
import 'Dashboard/Notifications/screens/notification_settings_screen.dart';
import 'Dashboard/Notifications/screens/notification_retention_settings_screen.dart';
import 'Dashboard/Notifications/screens/cattle_notification_settings_screen.dart';
import 'Dashboard/Notifications/controllers/notification_center_controller.dart';
import 'Dashboard/Notifications/controllers/notification_settings_controller.dart';

// User Account screens
import 'Dashboard/User Account/screens/welcome_screen.dart';
import 'Dashboard/User Account/screens/features_screen.dart';
import 'Dashboard/User Account/screens/login_screen.dart';
import 'Dashboard/User Account/screens/registration_screen.dart';
import 'Dashboard/User Account/screens/password_reset_screen.dart';
import 'Dashboard/User Account/screens/user_account_screen.dart';
import 'Dashboard/User Account/screens/user_profile_edit_screen.dart';
import 'Dashboard/User Account/screens/user_settings_screen.dart';
import 'Dashboard/User Account/widgets/auth_wrapper.dart';
import 'Dashboard/User Account/controllers/auth_controller.dart';

// Other imports
import 'Dashboard/Farm Setup/services/farm_setup_repository.dart';
import 'Dashboard/User Account/services/auth_service.dart';
import 'Dashboard/Cattle/models/cattle_isar.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Logging Service first
  final loggingService = LoggingService();
  loggingService.setupLogging();
  loggingService.logUncaughtExceptions();

  // Add specific error handling for Firebase Auth platform channel errors
  FlutterError.onError = (FlutterErrorDetails details) {
    final exception = details.exception;
    if (exception.toString().contains('firebase_auth_platform_interface') &&
        exception.toString().contains('channel-error')) {
      // Log Firebase Auth platform channel errors as warnings instead of errors
      loggingService.warning('Firebase Auth platform channel error (app continues normally): ${exception.toString()}');
      return; // Don't crash the app for these errors
    }
    // For all other errors, use the default error handling
    FlutterError.presentError(details);
  };

  loggingService.info('🚀 [APP_LAUNCH] Starting Cattle Manager App initialization');



  // Initialize dependencies using the new DI system
  bool dependenciesInitialized = false;

  try {
    // Initialize all dependencies (replaces IsarInitializer)
    loggingService.info('🔧 [APP_LAUNCH] Initializing dependencies...');
    await DependencyInjection.initDependencies(loggingService);
    dependenciesInitialized = true;
    loggingService.info('✅ [APP_LAUNCH] Dependencies initialization succeeded');

    if (dependenciesInitialized) {
      // Get services from GetIt (using new DI pattern)
      final getIt = GetIt.instance;

      // STEP 1: Seed default data FIRST to ensure database has required data
      loggingService.info('🔧 [APP_LAUNCH] Seeding default data...');
      final dataSeeder = getIt<DefaultDataSeeder>();
      await dataSeeder.seedInitialData();
      loggingService.info('✅ [APP_LAUNCH] Default data seeding completed');

      // STEP 2: Initialize authentication service AFTER data seeding
      loggingService.info('🔧 [APP_LAUNCH] Initializing authentication service...');
      final authService = getIt<AuthService>();
      await authService.initialize();
      loggingService.info('✅ [APP_LAUNCH] Authentication service initialized');

      // STEP 3: Initialize auto backup service
      loggingService.info('🔧 [APP_LAUNCH] Initializing auto backup service...');
      final autoBackupService = getIt<AutoBackupService>();
      await autoBackupService.initialize();
      loggingService.info('✅ [APP_LAUNCH] Auto backup service initialized');
      
      // STEP 4: Initialize notification system
      loggingService.info('🔧 [APP_LAUNCH] Initializing notification system...');
      try {
        final notificationService = getIt<NotificationService>();
        await notificationService.initialize();
        
        final notificationAutomationService = getIt<NotificationAutomationService>();
        await notificationAutomationService.initialize();
        
        final eventNotificationIntegrationService = getIt<EventNotificationIntegrationService>();
        await eventNotificationIntegrationService.initialize();
        
        final notificationLifecycleService = getIt<NotificationLifecycleService>();
        await notificationLifecycleService.initialize();
        
        loggingService.info('✅ [APP_LAUNCH] Notification system initialized');
      } catch (e) {
        loggingService.warning('⚠️ [APP_LAUNCH] Error initializing notification system: $e');
      }

      // STEP 3: Verify database state for debugging
      final farmSetupRepository = getIt<FarmSetupRepository>();

      // Log database information for debugging
      final animalTypes = await farmSetupRepository.getAllAnimalTypes();
      loggingService
          .info('Loaded $animalTypes.length animal types from database');

      final breedCategories = await farmSetupRepository.getAllBreedCategories();
      loggingService
          .info('Loaded $breedCategories.length breed categories from database');

      final farms = await farmSetupRepository.getAllFarms();
      if (farms.isNotEmpty) {
        loggingService.info('Loaded farm: $farms.first.name');
      } else {
        loggingService.info('No farm found in database - this should not happen after seeding');
      }

      // STEP 4: Initialize backup settings safely (after farm creation)
      try {
        final backupSettings = await farmSetupRepository.getBackupSettings();
        loggingService.info('✅ [APP_LAUNCH] Backup settings initialized: $backupSettings.backupLocation');
      } catch (e) {
        loggingService.warning('⚠️ [APP_LAUNCH] Could not initialize backup settings: $e');
      }

    }
  } catch (e) {
    loggingService.error('❌ [APP_LAUNCH] Error initializing application: $e');
    dependenciesInitialized = false;
  }

  loggingService.info('🚀 [APP_LAUNCH] Starting Flutter app with dependencies initialized: $dependenciesInitialized');

  runApp(
    ChangeNotifierProvider(
      create: (context) => AuthController(),
      child: CattleManagerApp(
        isarInitialized: dependenciesInitialized,
      ),
    ),
  );
}

class CattleManagerApp extends StatefulWidget {
  final bool isarInitialized;

  const CattleManagerApp({
    super.key,
    required this.isarInitialized,
  });

  @override
  State<CattleManagerApp> createState() => _CattleManagerAppState();


}

class _CattleManagerAppState extends State<CattleManagerApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cattle Manager',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
        useMaterial3: true,
        scaffoldBackgroundColor: AppTheme.scaffoldBackground,
        appBarTheme: AppTheme.appBarTheme,
        cardTheme: CardThemeData(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: AppTheme.cardRadius),
        ),
      ),
      initialRoute: widget.isarInitialized ? '/' : '/database-error',
      routes: {
        // Root route - determines what to show based on auth state
        AppRoutes.dashboard: (context) => const AuthWrapper(),
        '/database-error': (context) => const DatabaseErrorScreen(),

        // All screens now use the Provider pattern with automatic controller lifecycle management
        AppRoutes.cattle: (context) => ChangeNotifierProvider(
          create: (context) => CattleController()..loadData(),
          child: const CattleScreen(),
        ),
        AppRoutes.breeding: (context) => const BreedingScreen(), // Provider managed internally
        AppRoutes.health: (context) => const HealthScreen(), // Provider managed internally
        AppRoutes.milk: (context) => const MilkScreen(), // Provider managed internally
        AppRoutes.transactions: (context) => const TransactionsScreen(), // Provider managed internally

        AppRoutes.events: (context) => const EventsScreen(), // Provider managed internally
        AppRoutes.weight: (context) => const WeightScreen(), // Provider managed internally
        AppRoutes.reports: (context) => const ReportsScreen(), // Provider managed internally
        AppRoutes.settings: (context) => const UserSettingsScreen(),

        // Notification routes
        AppRoutes.notifications: (context) => const NotificationsScreen(),
        AppRoutes.notificationCenter: (context) => ChangeNotifierProvider(
          create: (context) => NotificationCenterController(),
          child: const NotificationCenterScreen(),
        ),
        AppRoutes.notificationSettings: (context) => ChangeNotifierProvider(
          create: (context) => NotificationSettingsController(),
          child: const NotificationSettingsScreen(),
        ),
        AppRoutes.notificationRetentionSettings: (context) => ChangeNotifierProvider(
          create: (context) => NotificationSettingsController(),
          child: const NotificationRetentionSettingsScreen(),
        ),
        AppRoutes.cattleNotificationSettings: (context) {
          // For now, we'll create a placeholder cattle object
          // In a real app, this would be passed as route arguments
          final dummyCattle = CattleIsar()
            ..businessId = 'demo-cattle-001'
            ..name = 'Demo Cattle'
            ..tagId = 'DEMO001';
          return ChangeNotifierProvider(
            create: (context) => NotificationSettingsController(),
            child: CattleNotificationSettingsScreen(cattle: dummyCattle),
          );
        },

        // User Account routes
        AppRoutes.welcome: (context) => const WelcomeScreen(),
        AppRoutes.features: (context) => const FeaturesScreen(),
        AppRoutes.login: (context) => const LoginScreen(),
        AppRoutes.register: (context) => const RegistrationScreen(),
        AppRoutes.passwordReset: (context) => const PasswordResetScreen(),
        AppRoutes.userAccount: (context) => const UserAccountScreen(),
        AppRoutes.userProfile: (context) => const UserProfileEditScreen(),
        AppRoutes.userSettings: (context) => const UserSettingsScreen(),
        // AppRoutes.transactionsReport: (context) =>
        //     const TransactionsReportScreen(),
        // AppRoutes.milkReport: (context) => const MilkReportScreen(),
        // AppRoutes.cattleReport: (context) => const CattleReportScreen(),
        // AppRoutes.eventsReport: (context) => const EventsReportScreen(),
        // AppRoutes.breedingReport: (context) => const BreedingReportScreen(),
        // AppRoutes.pregnanciesReport: (context) =>
        //     const PregnanciesReportScreen(),
        // AppRoutes.weightReport: (context) => const WeightReportScreen(),
        // AppRoutes.healthReport: (context) => const HealthReportScreen(),
      },
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''),
      ],
    );
  }
}

class DatabaseErrorScreen extends StatelessWidget {
  const DatabaseErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              const Text(
                'Database Error',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'The database failed to initialize. Please restart the app or contact support.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // You could add a retry mechanism here if needed
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const CattleManagerApp(
                        isarInitialized: false,
                      ),
                    ),
                  );
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
