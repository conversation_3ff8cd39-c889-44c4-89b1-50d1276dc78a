import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img;

import '../models/event_attachment_isar.dart';
import '../services/events_repository.dart';
import '../../../services/auto_backup_service.dart';

/// Service for managing event attachments (photos and documents)
/// 
/// This service handles:
/// - Image picking from camera or gallery
/// - Document picking from file system
/// - Thumbnail generation for images
/// - File storage and management
/// - Integration with backup system
class EventAttachmentService {
  final EventsRepository _eventsRepository = GetIt.instance<EventsRepository>();
  final AutoBackupService _autoBackupService = GetIt.instance<AutoBackupService>();
  final ImagePicker _imagePicker = ImagePicker();

  /// Pick and attach an image to an event
  Future<EventAttachmentIsar?> pickAndAttachImage({
    required String eventBusinessId,
    required BuildContext context,
    ImageSource? source,
  }) async {
    try {
      // Show source selection if not specified
      final selectedSource = source ?? await _showImageSourceDialog(context);
      if (selectedSource == null) return null;

      // Pick image
      final XFile? image = await _imagePicker.pickImage(
        source: selectedSource,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image == null) return null;

      // Get file info
      final file = File(image.path);
      final fileSize = await file.length();
      final fileName = image.name;

      // Create storage directory
      final directory = await _getAttachmentsDirectory();
      final storedFileName = 'event_${eventBusinessId}_${DateTime.now().millisecondsSinceEpoch}_$fileName';
      final storedFile = File('${directory.path}/$storedFileName');

      // Copy file to storage
      await file.copy(storedFile.path);

      // Get image dimensions
      final imageBytes = await storedFile.readAsBytes();
      final decodedImage = img.decodeImage(imageBytes);
      final imageWidth = decodedImage?.width;
      final imageHeight = decodedImage?.height;

      // Generate thumbnail
      final thumbnailPath = await _generateThumbnail(storedFile.path, directory.path);

      // Create attachment record
      final attachment = EventAttachmentIsar.fromImage(
        eventBusinessId: eventBusinessId,
        fileName: fileName,
        filePath: storedFile.path,
        fileSize: fileSize,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
        thumbnailPath: thumbnailPath,
      );

      // Save to database
      await _eventsRepository.saveEventAttachment(attachment);

      // Trigger backup after attachment added
      _autoBackupService.triggerBackupAfterDataChange(
        changeType: 'event_attachment_added',
        entityId: attachment.businessId,
        metadata: {
          'eventBusinessId': eventBusinessId,
          'fileName': fileName,
          'fileType': 'image',
        },
      );

      return attachment;
    } catch (e) {
      //$1// $0 // Print statement commented out for production
      return null;
    }
  }

  /// Pick and attach a document to an event
  Future<EventAttachmentIsar?> pickAndAttachDocument({
    required String eventBusinessId,
    required BuildContext context,
  }) async {
    try {
      // Pick document
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final pickedFile = result.files.first;
      if (pickedFile.path == null) return null;

      final file = File(pickedFile.path!);
      final fileSize = pickedFile.size;
      final fileName = pickedFile.name;

      // Create storage directory
      final directory = await _getAttachmentsDirectory();
      final storedFileName = 'event_${eventBusinessId}_${DateTime.now().millisecondsSinceEpoch}_$fileName';
      final storedFile = File('${directory.path}/$storedFileName');

      // Copy file to storage
      await file.copy(storedFile.path);

      // Create attachment record
      final attachment = EventAttachmentIsar.fromDocument(
        eventBusinessId: eventBusinessId,
        fileName: fileName,
        filePath: storedFile.path,
        fileSize: fileSize,
      );

      // Save to database
      await _eventsRepository.saveEventAttachment(attachment);

      // Trigger backup after attachment added
      _autoBackupService.triggerBackupAfterDataChange(
        changeType: 'event_attachment_added',
        entityId: attachment.businessId,
        metadata: {
          'eventBusinessId': eventBusinessId,
          'fileName': fileName,
          'fileType': 'document',
        },
      );

      return attachment;
    } catch (e) {
      //$1// $0 // Print statement commented out for production
      return null;
    }
  }

  /// Delete an attachment and its files
  Future<bool> deleteAttachment(EventAttachmentIsar attachment) async {
    try {
      // Delete files from storage
      if (attachment.filePath != null) {
        final file = File(attachment.filePath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // Delete thumbnail if exists
      if (attachment.thumbnailPath != null) {
        final thumbnailFile = File(attachment.thumbnailPath!);
        if (await thumbnailFile.exists()) {
          await thumbnailFile.delete();
        }
      }

      // Delete from database
      await _eventsRepository.deleteEventAttachment(attachment.id);

      // Trigger backup after attachment deleted
      _autoBackupService.triggerBackupAfterDataChange(
        changeType: 'event_attachment_deleted',
        entityId: attachment.businessId,
        metadata: {
          'eventBusinessId': attachment.eventBusinessId,
          'fileName': attachment.fileName,
          'fileType': attachment.fileType,
        },
      );

      return true;
    } catch (e) {
      //$1// $0 // Print statement commented out for production
      return false;
    }
  }

  /// Get all attachments for an event
  Future<List<EventAttachmentIsar>> getEventAttachments(String eventBusinessId) async {
    return await _eventsRepository.getAttachmentsForEvent(eventBusinessId);
  }

  /// Get attachments directory
  Future<Directory> _getAttachmentsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final attachmentsDir = Directory('${appDir.path}/event_attachments');
    
    if (!await attachmentsDir.exists()) {
      await attachmentsDir.create(recursive: true);
    }
    
    return attachmentsDir;
  }

  /// Show image source selection dialog
  Future<ImageSource?> _showImageSourceDialog(BuildContext context) async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Image Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                onTap: () => Navigator.of(context).pop(ImageSource.camera),
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () => Navigator.of(context).pop(ImageSource.gallery),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Generate thumbnail for image
  Future<String?> _generateThumbnail(String imagePath, String storageDir) async {
    try {
      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);
      
      if (originalImage == null) return null;

      // Create thumbnail (200x200 max, maintaining aspect ratio)
      final thumbnail = img.copyResize(
        originalImage,
        width: 200,
        height: 200,
        maintainAspect: true,
      );

      // Save thumbnail
      final thumbnailFileName = 'thumb_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final thumbnailPath = '$storageDir/$thumbnailFileName';
      final thumbnailFile = File(thumbnailPath);
      
      await thumbnailFile.writeAsBytes(img.encodeJpg(thumbnail, quality: 80));
      
      return thumbnailPath;
    } catch (e) {
      //$1// $0 // Print statement commented out for production
      return null;
    }
  }

  /// Clean up orphaned attachment files
  Future<void> cleanupOrphanedFiles() async {
    try {
      final directory = await _getAttachmentsDirectory();
      final allAttachments = await _eventsRepository.getAllEventAttachments();
      
      // Get all valid file paths
      final validPaths = <String>{};
      for (final attachment in allAttachments) {
        if (attachment.filePath != null) {
          validPaths.add(attachment.filePath!);
        }
        if (attachment.thumbnailPath != null) {
          validPaths.add(attachment.thumbnailPath!);
        }
      }

      // Check all files in directory
      final files = directory.listSync();
      for (final file in files) {
        if (file is File && !validPaths.contains(file.path)) {
          // This file is orphaned, delete it
          await file.delete();
          //$1// $0 // Print statement commented out for production
        }
      }
    } catch (e) {
      //$1// $0 // Print statement commented out for production
    }
  }

  /// Get total storage used by attachments
  Future<int> getTotalStorageUsed() async {
    try {
      final directory = await _getAttachmentsDirectory();
      int totalSize = 0;
      
      final files = directory.listSync();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      //$1// $0 // Print statement commented out for production
      return 0;
    }
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Check if file exists
  Future<bool> fileExists(String? filePath) async {
    if (filePath == null) return false;
    return await File(filePath).exists();
  }

  /// Get file extension from path
  static String getFileExtension(String filePath) {
    return filePath.split('.').last.toLowerCase();
  }

  /// Check if file is an image based on extension
  static bool isImageFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Check if file is a document based on extension
  static bool isDocumentFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['pdf', 'doc', 'docx', 'txt', 'xlsx', 'xls', 'ppt', 'pptx'].contains(extension);
  }
}
