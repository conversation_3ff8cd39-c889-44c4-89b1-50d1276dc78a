import 'notification_priority.dart';

/// Request model for sending push notifications
class PushNotificationRequest {
  /// Title of the push notification
  final String title;
  
  /// Body content of the push notification
  final String body;
  
  /// URL to an image to display in the notification
  final String? imageUrl;
  
  /// Additional data payload for the notification
  final Map<String, String>? data;
  
  /// FCM topic to send to (for topic-based messaging)
  final String? topic;
  
  /// List of FCM tokens to send to (for direct device messaging)
  final List<String>? tokens;
  
  /// Priority level of the notification
  final NotificationPriority priority;
  
  /// Action to perform when notification is clicked
  final String? clickAction;
  
  /// Sound to play with the notification
  final String? sound;
  
  /// Badge count to display (iOS)
  final String? badge;

  /// Constructor
  PushNotificationRequest({
    required this.title,
    required this.body,
    this.imageUrl,
    this.data,
    this.topic,
    this.tokens,
    this.priority = NotificationPriority.medium,
    this.clickAction,
    this.sound,
    this.badge,
  });

  /// Convert to a map for FCM payload
  Map<String, dynamic> toFcmPayload() {
    final Map<String, dynamic> notification = {
      'title': title,
      'body': body,
    };
    
    if (imageUrl != null) {
      notification['image'] = imageUrl;
    }
    
    if (sound != null) {
      notification['sound'] = sound;
    }
    
    if (badge != null) {
      notification['badge'] = badge;
    }
    
    final Map<String, dynamic> androidConfig = {
      'priority': priority == NotificationPriority.high || 
                  priority == NotificationPriority.critical
          ? 'high'
          : 'normal',
    };
    
    if (clickAction != null) {
      androidConfig['clickAction'] = clickAction;
    }
    
    final Map<String, dynamic> payload = {
      'notification': notification,
      'android': androidConfig,
      'data': data ?? {},
    };
    
    if (topic != null) {
      payload['topic'] = topic;
    } else if (tokens != null && tokens!.isNotEmpty) {
      payload['registration_ids'] = tokens;
    }
    
    return payload;
  }

  /// Convert to a map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'data': data,
      'topic': topic,
      'tokens': tokens,
      'priority': priority.index,
      'clickAction': clickAction,
      'sound': sound,
      'badge': badge,
    };
  }

  /// Create from a map (JSON deserialization)
  factory PushNotificationRequest.fromMap(Map<String, dynamic> map) {
    return PushNotificationRequest(
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      imageUrl: map['imageUrl'],
      data: map['data'] != null 
          ? Map<String, String>.from(map['data']) 
          : null,
      topic: map['topic'],
      tokens: map['tokens'] != null 
          ? List<String>.from(map['tokens']) 
          : null,
      priority: map['priority'] != null 
          ? NotificationPriority.values[map['priority']] 
          : NotificationPriority.medium,
      clickAction: map['clickAction'],
      sound: map['sound'],
      badge: map['badge'],
    );
  }
}