import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/report_models.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../Milk Records/services/milk_repository.dart';
import '../../Health/services/health_repository.dart';
import '../../Breeding/services/breeding_repository.dart';
import '../../Transactions/services/transactions_repository.dart';
import 'performance_monitor.dart';
import '../../../core/get_stub.dart';

/// Unified Reports Service
/// 
/// Aggregates data from all modules to create comprehensive reports.
/// Integrates with existing controllers and provides caching for performance.
class ReportsService {
  static final ReportsService _instance = ReportsService._internal();
  factory ReportsService() => _instance;
  ReportsService._internal();

  // Controllers for data access
  final CattleController _cattleController = Get.find<CattleController>();
  final MilkController _milkController = Get.find<MilkController>();
  final HealthController _healthController = Get.find<HealthController>();
  final BreedingController _breedingController = Get.find<BreedingController>();
  // final WeightController _weightController = Get.find<WeightController>(); // Unused for now
  final TransactionController _transactionController = Get.find<TransactionController>();

  /// Generate dashboard report with key metrics from all modules
  Future<ReportData> getDashboardReport(FilterState filter) async {
    return await PerformanceMonitor.measureLoadTime(
      'Dashboard Report Generation',
      () async {
        try {
          final metrics = <String, ReportMetric>{};
          final chartData = <ChartPoint>[];
          final tableData = <Map<String, String>>[];
          final insights = <String>[];

      // Get cattle metrics
      final cattle = _cattleController.cattle;
      final activeCattle = cattle.where((c) => c.status.toString() == 'Active').length;
      
      metrics['total_cattle'] = ReportMetric.kpi(
        title: 'Total Cattle',
        value: cattle.length.toString(),
        icon: Icons.pets,
        color: Colors.brown,
        subtitle: '$activeCattle active',
      );

      // Get milk production metrics
      final milkRecords = _milkController.milkRecords;
      final recentMilk = _filterByDate(milkRecords, filter);
      final totalMilk = recentMilk.fold<double>(0, (sum, record) => sum + (record.quantity ?? 0));
      
      metrics['milk_production'] = ReportMetric.kpi(
        title: 'Milk Production',
        value: '${totalMilk.toStringAsFixed(1)}L',
        icon: Icons.local_drink,
        color: Colors.lightBlue,
        subtitle: _getDateRangeText(filter),
      );

      // Get health metrics
      final healthRecords = _healthController.healthRecords;
      final recentHealth = _filterByDate(healthRecords, filter);
      final healthIssues = recentHealth.where((h) => h.status == 'Active').length;
      
      metrics['health_status'] = ReportMetric.withBadge(
        title: 'Health Issues',
        value: healthIssues.toString(),
        icon: Icons.health_and_safety,
        color: healthIssues > 0 ? Colors.red : Colors.green,
        badge: healthIssues > 0 ? 'Attention' : 'Good',
      );

      // Get breeding metrics
      final breedingRecords = _breedingController.breedingRecords;
      final activePregnancies = breedingRecords.where((b) => b.status == 'Pregnant').length;
      
      metrics['breeding_status'] = ReportMetric.kpi(
        title: 'Pregnancies',
        value: activePregnancies.toString(),
        icon: Icons.favorite,
        color: Colors.pink,
        subtitle: 'Active',
      );

      // Get financial metrics
      final transactions = _transactionController.transactions;
      final recentTransactions = _filterByDate(transactions, filter);
      final income = recentTransactions
          .where((t) => t.type == 'Income')
          .fold<double>(0, (sum, t) => sum + (t.amount ?? 0));
      final expenses = recentTransactions
          .where((t) => t.type == 'Expense')
          .fold<double>(0, (sum, t) => sum + (t.amount ?? 0));
      final profit = income - expenses;
      
      metrics['financial_summary'] = ReportMetric.withInsight(
        title: 'Net Profit',
        value: '\$${profit.toStringAsFixed(2)}',
        icon: Icons.attach_money,
        color: profit >= 0 ? Colors.green : Colors.red,
        insight: profit >= 0 ? 'Profitable' : 'Loss',
      );

      // Generate chart data for milk production trend
      chartData.addAll(_generateMilkTrendData(milkRecords, filter));

      // Generate insights
      insights.addAll(_generateDashboardInsights(metrics, filter));

          // Optimize chart data for performance
          final optimizedChartData = ChartDataPaginator.paginateForChart(chartData);

          // Check performance and log recommendations
          PerformanceMonitor.checkMemoryUsage('Dashboard Report Generated');

          return ReportData(
            title: 'Farm Dashboard',
            subtitle: 'Overview of farm operations',
            generated: DateTime.now(),
            startDate: filter.startDate,
            endDate: filter.endDate,
            metrics: metrics,
            chartData: optimizedChartData,
            tableData: tableData,
            insights: insights,
            type: ReportType.dashboard,
          );
        } catch (e) {
          return ReportData.empty(ReportType.dashboard);
        }
      },
    );
  }

  /// Generate cattle report
  Future<ReportData> getCattleReport(FilterState filter) async {
    try {
      final cattle = _cattleController.cattle;
      final filteredCattle = _filterCattleByIds(cattle, filter.cattleIds);
      
      final metrics = <String, ReportMetric>{};
      final chartData = <ChartPoint>[];
      final tableData = <Map<String, String>>[];
      final insights = <String>[];

      // Total cattle metric
      metrics['total_cattle'] = ReportMetric.kpi(
        title: 'Total Cattle',
        value: filteredCattle.length.toString(),
        icon: Icons.pets,
        color: Colors.brown,
      );

      // Cattle by status
      final statusCounts = <String, int>{};
      for (final animal in filteredCattle) {
        statusCounts[animal.status ?? 'Unknown'] = 
            (statusCounts[animal.status ?? 'Unknown'] ?? 0) + 1;
      }

      // Generate chart data for cattle status distribution
      chartData.addAll(statusCounts.entries.map((entry) => 
        ChartPoint.category(
          category: entry.key,
          value: entry.value.toDouble(),
          color: _getStatusColor(entry.key),
        )
      ));

      // Generate table data
      tableData.addAll(filteredCattle.map((animal) => {
        'Tag': animal.tagNumber ?? 'N/A',
        'Name': animal.name ?? 'N/A',
        'Breed': animal.breed ?? 'N/A',
        'Status': animal.status ?? 'N/A',
        'Age': _calculateAge(animal.dateOfBirth),
      }));

      // Generate insights
      insights.add('${filteredCattle.length} cattle in your herd');
      if (statusCounts.containsKey('Active')) {
        insights.add('${statusCounts['Active']} cattle are currently active');
      }

      return ReportData(
        title: 'Cattle Report',
        subtitle: 'Comprehensive cattle inventory',
        generated: DateTime.now(),
        startDate: filter.startDate,
        endDate: filter.endDate,
        metrics: metrics,
        chartData: chartData,
        tableData: tableData,
        insights: insights,
        type: ReportType.cattle,
      );
    } catch (e) {
      return ReportData.empty(ReportType.cattle);
    }
  }

  /// Generate milk production report
  Future<ReportData> getMilkReport(FilterState filter) async {
    try {
      final milkRecords = _milkController.milkRecords;
      final filteredRecords = _filterByDate(milkRecords, filter);
      
      final metrics = <String, ReportMetric>{};
      final chartData = <ChartPoint>[];
      final tableData = <Map<String, String>>[];
      final insights = <String>[];

      // Total production
      final totalProduction = filteredRecords.fold<double>(
        0, (sum, record) => sum + (record.quantity ?? 0));
      
      metrics['total_production'] = ReportMetric.kpi(
        title: 'Total Production',
        value: '${totalProduction.toStringAsFixed(1)}L',
        icon: Icons.local_drink,
        color: Colors.lightBlue,
      );

      // Average daily production
      final days = _getDaysBetween(filter.startDate, filter.endDate);
      final avgDaily = days > 0 ? totalProduction / days : 0;
      
      metrics['avg_daily'] = ReportMetric.kpi(
        title: 'Daily Average',
        value: '${avgDaily.toStringAsFixed(1)}L',
        icon: Icons.today,
        color: Colors.blue,
      );

      // Generate trend chart data
      chartData.addAll(_generateMilkTrendData(filteredRecords, filter));

      // Generate table data
      tableData.addAll(filteredRecords.map((record) => {
        'Date': _formatDate(record.date),
        'Cattle': record.cattleTagNumber ?? 'N/A',
        'Quantity': '${record.quantity?.toStringAsFixed(1) ?? '0'}L',
        'Session': record.session ?? 'N/A',
      }));

      // Generate insights
      insights.add('Total production: ${totalProduction.toStringAsFixed(1)}L');
      insights.add('Average daily: ${avgDaily.toStringAsFixed(1)}L');
      if (filteredRecords.isNotEmpty) {
        final bestDay = filteredRecords.reduce((a, b) => 
          (a.quantity ?? 0) > (b.quantity ?? 0) ? a : b);
        insights.add('Best day: ${bestDay.quantity?.toStringAsFixed(1)}L on ${_formatDate(bestDay.date)}');
      }

      return ReportData(
        title: 'Milk Production Report',
        subtitle: 'Detailed milk production analysis',
        generated: DateTime.now(),
        startDate: filter.startDate,
        endDate: filter.endDate,
        metrics: metrics,
        chartData: chartData,
        tableData: tableData,
        insights: insights,
        type: ReportType.milk,
      );
    } catch (e) {
      return ReportData.empty(ReportType.milk);
    }
  }

  /// Generate health report
  Future<ReportData> getHealthReport(FilterState filter) async {
    try {
      final healthRecords = _healthController.healthRecords;
      final filteredRecords = _filterByDate(healthRecords, filter);
      
      final metrics = <String, ReportMetric>{};
      final chartData = <ChartPoint>[];
      final tableData = <Map<String, String>>[];
      final insights = <String>[];

      // Total health records
      metrics['total_records'] = ReportMetric.kpi(
        title: 'Health Records',
        value: filteredRecords.length.toString(),
        icon: Icons.health_and_safety,
        color: Colors.red,
      );

      // Active issues
      final activeIssues = filteredRecords.where((r) => r.status == 'Active').length;
      metrics['active_issues'] = ReportMetric.withBadge(
        title: 'Active Issues',
        value: activeIssues.toString(),
        icon: Icons.warning,
        color: activeIssues > 0 ? Colors.red : Colors.green,
        badge: activeIssues > 0 ? 'Attention' : 'Good',
      );

      // Generate chart data for health categories
      final categoryCounts = <String, int>{};
      for (final record in filteredRecords) {
        final category = record.category ?? 'Other';
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }

      chartData.addAll(categoryCounts.entries.map((entry) => 
        ChartPoint.category(
          category: entry.key,
          value: entry.value.toDouble(),
          color: _getHealthCategoryColor(entry.key),
        )
      ));

      // Generate table data
      tableData.addAll(filteredRecords.map((record) => {
        'Date': _formatDate(record.date),
        'Cattle': record.cattleTagNumber ?? 'N/A',
        'Category': record.category ?? 'N/A',
        'Description': record.description ?? 'N/A',
        'Status': record.status ?? 'N/A',
      }));

      // Generate insights
      insights.add('${filteredRecords.length} health records in period');
      insights.add('$activeIssues active health issues');
      if (categoryCounts.isNotEmpty) {
        final topCategory = categoryCounts.entries.reduce((a, b) => 
          a.value > b.value ? a : b);
        insights.add('Most common: ${topCategory.key} (${topCategory.value} cases)');
      }

      return ReportData(
        title: 'Health Records Report',
        subtitle: 'Health monitoring and treatment records',
        generated: DateTime.now(),
        startDate: filter.startDate,
        endDate: filter.endDate,
        metrics: metrics,
        chartData: chartData,
        tableData: tableData,
        insights: insights,
        type: ReportType.health,
      );
    } catch (e) {
      return ReportData.empty(ReportType.health);
    }
  }

  /// Generate financial report
  Future<ReportData> getFinancialReport(FilterState filter) async {
    try {
      final transactions = _transactionController.transactions;
      final filteredTransactions = _filterByDate(transactions, filter);
      
      final metrics = <String, ReportMetric>{};
      final chartData = <ChartPoint>[];
      final tableData = <Map<String, String>>[];
      final insights = <String>[];

      // Calculate totals
      final income = filteredTransactions
          .where((t) => t.type == 'Income')
          .fold<double>(0, (sum, t) => sum + (t.amount ?? 0));
      final expenses = filteredTransactions
          .where((t) => t.type == 'Expense')
          .fold<double>(0, (sum, t) => sum + (t.amount ?? 0));
      final profit = income - expenses;

      // Income metric
      metrics['total_income'] = ReportMetric.kpi(
        title: 'Total Income',
        value: '\$${income.toStringAsFixed(2)}',
        icon: Icons.trending_up,
        color: Colors.green,
      );

      // Expenses metric
      metrics['total_expenses'] = ReportMetric.kpi(
        title: 'Total Expenses',
        value: '\$${expenses.toStringAsFixed(2)}',
        icon: Icons.trending_down,
        color: Colors.red,
      );

      // Net profit metric
      metrics['net_profit'] = ReportMetric.withInsight(
        title: 'Net Profit',
        value: '\$${profit.toStringAsFixed(2)}',
        icon: Icons.attach_money,
        color: profit >= 0 ? Colors.green : Colors.red,
        insight: profit >= 0 ? 'Profitable' : 'Loss',
      );

      // Generate chart data for income vs expenses
      chartData.addAll([
        ChartPoint.category(category: 'Income', value: income, color: Colors.green),
        ChartPoint.category(category: 'Expenses', value: expenses, color: Colors.red),
      ]);

      // Generate table data
      tableData.addAll(filteredTransactions.map((transaction) => {
        'Date': _formatDate(transaction.date),
        'Type': transaction.type ?? 'N/A',
        'Category': transaction.category ?? 'N/A',
        'Description': transaction.description ?? 'N/A',
        'Amount': '\$${transaction.amount?.toStringAsFixed(2) ?? '0.00'}',
      }));

      // Generate insights
      insights.add('Total income: \$${income.toStringAsFixed(2)}');
      insights.add('Total expenses: \$${expenses.toStringAsFixed(2)}');
      insights.add('Net profit: \$${profit.toStringAsFixed(2)}');
      if (profit > 0) {
        final margin = (profit / income * 100);
        insights.add('Profit margin: ${margin.toStringAsFixed(1)}%');
      }

      return ReportData(
        title: 'Financial Report',
        subtitle: 'Income, expenses, and profitability analysis',
        generated: DateTime.now(),
        startDate: filter.startDate,
        endDate: filter.endDate,
        metrics: metrics,
        chartData: chartData,
        tableData: tableData,
        insights: insights,
        type: ReportType.financial,
      );
    } catch (e) {
      return ReportData.empty(ReportType.financial);
    }
  }

  // Helper methods

  List<dynamic> _filterByDate(List<dynamic> records, FilterState filter) {
    if (filter.startDate == null && filter.endDate == null) return records;
    
    return records.where((record) {
      final date = record.date as DateTime?;
      if (date == null) return false;
      
      if (filter.startDate != null && date.isBefore(filter.startDate!)) return false;
      if (filter.endDate != null && date.isAfter(filter.endDate!)) return false;
      
      return true;
    }).toList();
  }

  List<dynamic> _filterCattleByIds(List<dynamic> cattle, List<String>? ids) {
    if (ids == null || ids.isEmpty) return cattle;
    return cattle.where((animal) => ids.contains(animal.id)).toList();
  }

  List<ChartPoint> _generateMilkTrendData(List<dynamic> records, FilterState filter) {
    final dailyTotals = <String, double>{};
    
    for (final record in records) {
      final date = record.date as DateTime?;
      if (date == null) continue;
      
      final dateKey = _formatDate(date);
      dailyTotals[dateKey] = (dailyTotals[dateKey] ?? 0) + (record.quantity ?? 0);
    }
    
    return dailyTotals.entries.map((entry) => 
      ChartPoint(
        label: entry.key,
        value: entry.value,
        color: Colors.lightBlue,
      )
    ).toList();
  }

  List<String> _generateDashboardInsights(Map<String, ReportMetric> metrics, FilterState filter) {
    final insights = <String>[];
    
    if (metrics.containsKey('total_cattle')) {
      insights.add('Managing ${metrics['total_cattle']!.value} cattle');
    }
    
    if (metrics.containsKey('milk_production')) {
      insights.add('Produced ${metrics['milk_production']!.value} of milk');
    }
    
    if (metrics.containsKey('health_status')) {
      final healthValue = int.tryParse(metrics['health_status']!.value) ?? 0;
      if (healthValue == 0) {
        insights.add('All cattle are healthy');
      } else {
        insights.add('$healthValue health issues need attention');
      }
    }
    
    return insights;
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'sold':
        return Colors.blue;
      case 'deceased':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getHealthCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'vaccination':
        return Colors.green;
      case 'treatment':
        return Colors.orange;
      case 'illness':
        return Colors.red;
      case 'checkup':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _calculateAge(DateTime? birthDate) {
    if (birthDate == null) return 'Unknown';
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final years = (difference.inDays / 365).floor();
    final months = ((difference.inDays % 365) / 30).floor();
    
    if (years > 0) {
      return '${years}y ${months}m';
    } else {
      return '${months}m';
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getDateRangeText(FilterState filter) {
    if (filter.startDate == null && filter.endDate == null) return 'All time';
    if (filter.startDate != null && filter.endDate != null) {
      return '${_formatDate(filter.startDate)} - ${_formatDate(filter.endDate)}';
    }
    return 'Filtered';
  }

  int _getDaysBetween(DateTime? start, DateTime? end) {
    if (start == null || end == null) return 30; // Default to 30 days
    return end.difference(start).inDays + 1;
  }
}