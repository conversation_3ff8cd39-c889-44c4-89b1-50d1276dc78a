import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:logging/logging.dart';

import '../models/notification_isar.dart';

/// Service for notification security and data protection
class NotificationSecurityService {
  final Logger _logger = Logger('NotificationSecurityService');
  
  // Simple encryption key (in production, this should be securely managed)
  static const String _encryptionKey = 'notification_encryption_key_2024';

  /// Encrypt sensitive notification data
  String encryptSensitiveData(String data) {
    try {
      if (data.isEmpty) return data;
      
      // Simple XOR encryption (in production, use proper encryption)
      final keyBytes = utf8.encode(_encryptionKey);
      final dataBytes = utf8.encode(data);
      final encrypted = <int>[];
      
      for (int i = 0; i < dataBytes.length; i++) {
        encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return base64.encode(encrypted);
    } catch (e) {
      _logger.severe('Error encrypting data: $e');
      return data; // Return original data if encryption fails
    }
  }

  /// Decrypt sensitive notification data
  String decryptSensitiveData(String encryptedData) {
    try {
      if (encryptedData.isEmpty) return encryptedData;
      
      final keyBytes = utf8.encode(_encryptionKey);
      final encryptedBytes = base64.decode(encryptedData);
      final decrypted = <int>[];
      
      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      _logger.severe('Error decrypting data: $e');
      return encryptedData; // Return encrypted data if decryption fails
    }
  }

  /// Sanitize notification content
  String sanitizeContent(String content) {
    if (content.isEmpty) return content;
    
    // Remove potentially harmful content
    String sanitized = content
        .replaceAll(RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'on\w+\s*=', caseSensitive: false), '')
        .replaceAll(RegExp(r'<iframe[^>]*>.*?</iframe>', caseSensitive: false), '');
    
    // Limit content length
    if (sanitized.length > 1000) {
      sanitized = '${sanitized.substring(0, 997)}...';
    }
    
    return sanitized.trim();
  }

  /// Validate notification access permissions
  bool validateNotificationAccess(String userId, NotificationIsar notification) {
    try {
      // Check if user has access to this notification
      // This is a simplified check - in production, implement proper RBAC
      
      if (userId.isEmpty) {
        _logger.warning('Empty user ID for notification access validation');
        return false;
      }
      
      // For now, allow access if notification doesn't have specific user restrictions
      // In production, implement proper user-based access control
      return true;
    } catch (e) {
      _logger.severe('Error validating notification access: $e');
      return false;
    }
  }

  /// Generate secure FCM token hash
  String generateTokenHash(String token) {
    try {
      final bytes = utf8.encode(token + _encryptionKey);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      _logger.severe('Error generating token hash: $e');
      return token;
    }
  }

  /// Validate FCM token integrity
  bool validateTokenIntegrity(String token, String expectedHash) {
    try {
      final actualHash = generateTokenHash(token);
      return actualHash == expectedHash;
    } catch (e) {
      _logger.severe('Error validating token integrity: $e');
      return false;
    }
  }

  /// Create audit log entry
  void createAuditLog(String action, String notificationId, String userId, {Map<String, dynamic>? metadata}) {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'action': action,
        'notificationId': notificationId,
        'userId': userId,
        'metadata': metadata ?? {},
      };
      
      _logger.info('Audit log: ${jsonEncode(logEntry)}');
      
      // In production, store audit logs in a secure, tamper-proof storage
    } catch (e) {
      _logger.severe('Error creating audit log: $e');
    }
  }

  /// Rate limiting check
  bool checkRateLimit(String userId, String action, {int maxRequests = 100, Duration window = const Duration(hours: 1)}) {
    try {
      // Simple rate limiting implementation
      // In production, use Redis or similar for distributed rate limiting
      
      final key = '${userId}_$action';
      // final now = DateTime.now();
      
      // This is a simplified implementation
      // In production, implement proper rate limiting with sliding windows
      
      _logger.fine('Rate limit check for $key: allowed');
      return true;
    } catch (e) {
      _logger.severe('Error checking rate limit: $e');
      return false;
    }
  }

  /// Secure FCM token storage
  Map<String, String> secureTokenStorage(String token) {
    try {
      // Generate a random salt
      final random = Random.secure();
      final salt = List.generate(16, (i) => random.nextInt(256));
      final saltString = base64.encode(salt);
      
      // Hash the token with salt
      final tokenWithSalt = token + saltString;
      final hashedToken = sha256.convert(utf8.encode(tokenWithSalt)).toString();
      
      return {
        'hashedToken': hashedToken,
        'salt': saltString,
      };
    } catch (e) {
      _logger.severe('Error in secure token storage: $e');
      return {'hashedToken': token, 'salt': ''};
    }
  }

  /// Verify stored FCM token
  bool verifyStoredToken(String token, String hashedToken, String salt) {
    try {
      final tokenWithSalt = token + salt;
      final computedHash = sha256.convert(utf8.encode(tokenWithSalt)).toString();
      return computedHash == hashedToken;
    } catch (e) {
      _logger.severe('Error verifying stored token: $e');
      return false;
    }
  }

  /// Data minimization - remove unnecessary fields
  Map<String, dynamic> minimizeNotificationData(NotificationIsar notification) {
    return {
      'id': notification.businessId,
      'title': notification.title,
      'message': notification.message,
      'category': notification.category,
      'priority': notification.priority.name,
      'status': notification.status.name,
      'createdAt': notification.createdAt?.toIso8601String(),
      // Exclude sensitive fields like custom data, internal IDs, etc.
    };
  }

  /// Validate input data
  bool validateNotificationInput(Map<String, dynamic> data) {
    try {
      // Check required fields
      if (data['title'] == null || data['title'].toString().isEmpty) {
        _logger.warning('Invalid notification input: missing title');
        return false;
      }
      
      // Check field lengths
      if (data['title'].toString().length > 100) {
        _logger.warning('Invalid notification input: title too long');
        return false;
      }
      
      if (data['message'] != null && data['message'].toString().length > 1000) {
        _logger.warning('Invalid notification input: message too long');
        return false;
      }
      
      return true;
    } catch (e) {
      _logger.severe('Error validating notification input: $e');
      return false;
    }
  }
}