name: cattle_manager
description: A comprehensive cattle management application for farmers and agricultural professionals.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# Version and build number for the application.
version: 1.18.0+118

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  logging: ^1.3.0
  printing: ^5.11.0  # Add printing package for PDF generation
  flutter_localizations:
    sdk: flutter
  sqflite: ^2.3.0
  sqflite_common_ffi: ^2.3.0
  sqflite_common_ffi_web: ^0.4.0
  path: ^1.8.3
  uuid: ^4.5.1
  intl: 0.20.2
  shared_preferences: ^2.2.2
  flutter_svg: ^2.0.17
  cupertino_icons: ^1.0.2
  font_awesome_flutter: ^10.8.0
  excel: ^4.0.6
  csv: ^5.1.1  # For CSV file handling
  # hive: ^2.2.3
  # hive_flutter: ^1.1.0
  pdf: ^3.10.4
  path_provider: ^2.1.5
  share_plus: ^7.2.1
  file_picker: ^8.0.7
  universal_html: ^2.2.4
  fl_chart: ^0.70.0
  syncfusion_flutter_xlsio: ^28.2.5
  syncfusion_flutter_pdf: ^28.2.5
  image_picker: ^1.1.2
  collection: ^1.19.0
  geolocator: ^10.1.0  # For GPS location
  geocoding: ^2.1.1  # For converting coordinates to addresses
  http: ^1.1.0  # For making HTTP requests
  googleapis: ^12.0.0  # For Google Drive API integration
  googleapis_auth: ^1.4.1  # For Google authentication
  google_sign_in: ^6.1.6  # For Google Sign-In integration
  mailer: ^6.0.1  # For sending emails via SMTP
  dart_jsonwebtoken: ^2.12.2  # For JWT token generation
  flutter_secure_storage: ^9.0.0  # For secure storage of credentials
  qr_flutter: ^4.1.0  # For QR code generation
  mobile_scanner: ^3.5.6  # For QR code scanning - modern replacement for qr_code_scanner
  url_launcher: ^6.2.5
  table_calendar: ^3.0.9  # For calendar functionality in health tracking
  keyboard_dismisser: ^2.0.0
  rxdart: ^0.28.0
  get_it: ^7.6.4
  # Using stable version of Isar
  isar: 3.1.0+1
  isar_flutter_libs: 3.1.0+1  # Contains native binaries
  json_annotation: ^4.8.1
  logger: ^2.5.0
  flutter_colorpicker: ^1.1.0
  permission_handler: ^11.4.0
  async: ^2.11.0
  connectivity_plus: ^6.1.4  # For network connectivity checking
  get: ^4.6.6  # For state management

  crypto: ^3.0.6
  local_auth: ^2.3.0  # For biometric authentication (fingerprint, face ID)
  firebase_core: ^2.27.1  # Firebase Core for initialization
  firebase_messaging: ^14.7.20  # Firebase Cloud Messaging for push notifications
  flutter_local_notifications: 19.3.1  # For handling local notifications

  image: any
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  # hive_generator: ^2.0.0
  build_runner: ^2.4.4  # Required for isar and mockito code generation
  # Stable Isar generator
  isar_generator: 3.1.0+1
  json_serializable: ^6.7.1
  mockito: ^5.4.4  # For testing with mocks

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/
