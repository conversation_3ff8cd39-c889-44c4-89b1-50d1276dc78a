import 'package:logging/logging.dart';

import 'notification_service.dart';
import 'push_notification_service.dart';
import 'offline_notification_service.dart';

/// Service for managing notification system lifecycle
class NotificationLifecycleService {
  final Logger _logger = Logger('NotificationLifecycleService');
  final NotificationService _notificationService;
  final PushNotificationService _pushService;
  final OfflineNotificationService _offlineService;

  bool _isInitialized = false;

  NotificationLifecycleService(
    this._notificationService,
    this._pushService,
    this._offlineService,
  );

  /// Initialize the notification system
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.info('Notification system already initialized');
      return;
    }

    try {
      _logger.info('Initializing notification system...');

      // Initialize push notification service
      await _pushService.initialize();
      _logger.info('Push notification service initialized');

      // Initialize offline service
      await _offlineService.initialize();
      _logger.info('Offline notification service initialized');

      // Initialize main notification service
      await _notificationService.initialize();
      _logger.info('Notification service initialized');

      // Start sync process
      await _offlineService.syncPendingOperations();
      _logger.info('Pending operations synced');

      _isInitialized = true;
      _logger.info('Notification system initialization completed');
    } catch (e) {
      _logger.severe('Error initializing notification system: $e');
      rethrow;
    }
  }

  /// Handle app lifecycle changes
  Future<void> handleAppLifecycleChange(String state) async {
    try {
      switch (state) {
        case 'resumed':
          _logger.info('App resumed - syncing notifications');
          await _offlineService.syncPendingOperations();
          break;
        case 'paused':
          _logger.info('App paused - cleaning up resources');
          break;
        case 'detached':
          _logger.info('App detached - performing cleanup');
          await dispose();
          break;
      }
    } catch (e) {
      _logger.severe('Error handling app lifecycle change: $e');
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    try {
      _logger.info('Disposing notification system...');
      
      _notificationService.dispose();
      _pushService.dispose();
      _offlineService.dispose();
      
      _isInitialized = false;
      _logger.info('Notification system disposed');
    } catch (e) {
      _logger.severe('Error disposing notification system: $e');
    }
  }

  /// Check if system is initialized
  bool get isInitialized => _isInitialized;
}