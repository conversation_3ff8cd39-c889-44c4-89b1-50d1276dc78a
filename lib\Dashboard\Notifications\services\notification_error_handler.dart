import 'dart:async';
import 'dart:convert';
import 'package:logging/logging.dart';

import 'notification_repository.dart';
import 'notification_settings_repository.dart';
import 'push_notification_service.dart';
import 'offline_notification_service.dart';
import '../models/notification_operation.dart';

/// Exception for network errors
class NetworkException implements Exception {
  /// The operation that failed
  final NotificationOperation operation;
  
  /// Error message
  final String message;
  
  /// Constructor
  NetworkException(this.operation, this.message);
  
  @override
  String toString() => 'NetworkException: $message';
}

/// Exception for FCM token errors
class FCMTokenException implements Exception {
  /// Error message
  final String message;
  
  /// Constructor
  FCMTokenException(this.message);
  
  @override
  String toString() => 'FCMTokenException: $message';
}

/// Exception for sync conflicts
class SyncConflictException implements Exception {
  /// The notification ID with the conflict
  final String notificationId;
  
  /// The server version of the notification
  final dynamic serverData;
  
  /// Constructor
  SyncConflictException(this.notificationId, this.serverData);
  
  @override
  String toString() => 'SyncConflictException: Conflict for notification $notificationId';
}

/// Exception for permission errors
class PermissionException implements Exception {
  /// The permission that was denied
  final String permission;
  
  /// Constructor
  PermissionException(this.permission);
  
  @override
  String toString() => 'PermissionException: $permission permission denied';
}

/// Service for handling notification errors and recovery
class NotificationErrorHandler {
  final Logger _logger = Logger('NotificationErrorHandler');
  final NotificationRepository _repository;
  final NotificationSettingsRepository _settingsRepository;
  final PushNotificationService _pushService;
  final OfflineNotificationService _offlineService;
  
  /// Function to show error messages to the user
  final void Function(String message) _showErrorMessage;
  
  /// Constructor with dependencies
  NotificationErrorHandler(
    this._repository,
    this._settingsRepository,
    this._pushService,
    this._offlineService,
    this._showErrorMessage,
  );
  
  /// Handle network errors
  Future<void> handleNetworkError(NetworkException error) async {
    try {
      _logger.warning('Network error: ${error.message}');
      
      // Queue operation for retry
      await _offlineService.queueOperation(
        error.operation.type,
        notificationId: error.operation.notificationId,
        data: error.operation.operationData != null
            ? jsonDecode(error.operation.operationData!)
            : null,
      );
      
      // Show user-friendly message
      _showErrorMessage('Network unavailable. Changes will sync when connected.');
    } catch (e) {
      _logger.severe('Error handling network error: $e');
    }
  }
  
  /// Handle FCM token errors
  Future<void> handleFCMTokenError(FCMTokenException error) async {
    try {
      _logger.warning('FCM token error: ${error.message}');
      
      // Attempt token refresh
      final newToken = await _pushService.refreshToken();
      
      if (newToken != null) {
        await _settingsRepository.updateFCMToken(newToken);
        _logger.info('FCM token refreshed successfully');
      } else {
        // Disable push notifications temporarily
        await _settingsRepository.setPushNotificationsEnabled(false);
        _showErrorMessage('Push notifications temporarily unavailable.');
        _logger.warning('FCM token refresh failed, push notifications disabled');
      }
    } catch (e) {
      _logger.severe('Error handling FCM token error: $e');
    }
  }
  
  /// Handle sync conflicts
  Future<void> handleSyncConflict(SyncConflictException error) async {
    try {
      _logger.warning('Sync conflict for notification: ${error.notificationId}');
      
      // Use server-side timestamp as authority
      final serverVersion = error.serverData;
      await _repository.saveNotification(serverVersion);
      
      // Log conflict for analytics
      _logger.info('Sync conflict resolved: ${error.notificationId}');
    } catch (e) {
      _logger.severe('Error handling sync conflict: $e');
    }
  }
  
  /// Handle permission errors
  Future<void> handlePermissionError(PermissionException error) async {
    try {
      _logger.warning('Permission error: ${error.permission}');
      
      switch (error.permission) {
        case 'notification':
          // Disable push notifications
          await _settingsRepository.setPushNotificationsEnabled(false);
          _showErrorMessage('Notification permission denied. Push notifications disabled.');
          break;
        default:
          _showErrorMessage('Permission denied: ${error.permission}');
      }
    } catch (e) {
      _logger.severe('Error handling permission error: $e');
    }
  }
  
  /// Handle database errors
  Future<void> handleDatabaseError(Exception error) async {
    try {
      _logger.severe('Database error: $error');
      
      // Show user-friendly message
      _showErrorMessage('Database error occurred. Please try again later.');
    } catch (e) {
      _logger.severe('Error handling database error: $e');
    }
  }
  
  /// Handle general errors with retry logic
  Future<T> withRetry<T>(Future<T> Function() operation, {int maxRetries = 3, Duration delay = const Duration(seconds: 1)}) async {
    int attempts = 0;
    
    while (true) {
      try {
        attempts++;
        return await operation();
      } catch (e) {
        if (attempts >= maxRetries) {
          _logger.severe('Operation failed after $attempts attempts: $e');
          rethrow;
        }
        
        _logger.warning('Operation failed (attempt $attempts/$maxRetries), retrying in ${delay.inSeconds}s: $e');
        await Future.delayed(delay * attempts); // Exponential backoff
      }
    }
  }
  
  /// Handle errors with graceful degradation
  Future<T?> withGracefulDegradation<T>(Future<T> Function() operation, T? fallbackValue) async {
    try {
      return await operation();
    } catch (e) {
      _logger.warning('Operation failed with graceful degradation: $e');
      return fallbackValue;
    }
  }
  
  /// Handle errors with transaction rollback
  Future<T> withTransaction<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } catch (e) {
      _logger.severe('Transaction failed: $e');
      // In a real implementation, we would roll back the transaction here
      rethrow;
    }
  }
  
  /// Handle errors with user guidance
  Future<T?> withUserGuidance<T>(Future<T> Function() operation, String errorMessage, String guidanceMessage) async {
    try {
      return await operation();
    } catch (e) {
      _logger.warning('$errorMessage: $e');
      _showErrorMessage(guidanceMessage);
      return null;
    }
  }
}