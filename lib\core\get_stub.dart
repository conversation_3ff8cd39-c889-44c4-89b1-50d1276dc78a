import 'package:flutter/material.dart';

/// Temporary stub class to replace GetX functionality
/// This allows the app to compile without GetX dependency
class Get {
  static void back() {
    // Stub implementation - does nothing
    debugPrint('Get.back() called - stub implementation');
  }
  
  static void snackbar(String title, String message, {
    Duration? duration,
    Color? backgroundColor,
    Color? colorText,
    Widget? mainButton,
  }) {
    // Stub implementation - does nothing
    debugPrint('Get.snackbar() called: $title - $message');
  }
  
  static void dialog(Widget dialog, {bool barrierDismissible = true}) {
    // Stub implementation - does nothing
    debugPrint('Get.dialog() called - stub implementation');
  }
}
