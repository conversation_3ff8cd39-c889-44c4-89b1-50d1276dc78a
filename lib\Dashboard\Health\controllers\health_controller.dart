import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:logging/logging.dart';

import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/health_repository.dart';
import '../services/health_analytics_service.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class HealthFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? cattleId;
  final String? recordType;
  final String? status;
  final String? vaccineType; // For vaccinations filter

  const HealthFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.cattleId,
    this.recordType,
    this.status,
    this.vaccineType,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (cattleId?.isNotEmpty == true && cattleId != 'All') ||
      (recordType?.isNotEmpty == true && recordType != 'All') ||
      (status?.isNotEmpty == true && status != 'All') ||
      (vaccineType?.isNotEmpty == true && vaccineType != 'All');

  /// Create a copy with updated values
  HealthFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? cattleId,
    String? recordType,
    String? status,
    String? vaccineType,
    bool clearStartDate = false,
    bool clearEndDate = false,
  }) {
    return HealthFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      cattleId: cattleId ?? this.cattleId,
      recordType: recordType ?? this.recordType,
      status: status ?? this.status,
      vaccineType: vaccineType ?? this.vaccineType,
    );
  }

  /// Clear all filters
  static const HealthFilterState empty = HealthFilterState();
}

/// Reactive controller for the main health screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class HealthController extends ChangeNotifier {
  static final Logger _logger = Logger('HealthController');

  // Repositories
  final HealthRepository _healthRepository = GetIt.instance<HealthRepository>();
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Individual stream subscriptions - NOT StreamZip (following cattle module pattern)
  StreamSubscription<List<HealthRecordIsar>>? _healthRecordsStreamSubscription;
  StreamSubscription<List<MedicationIsar>>? _medicationsStreamSubscription;
  StreamSubscription<List<TreatmentIsar>>? _treatmentsStreamSubscription;
  StreamSubscription<List<VaccinationIsar>>? _vaccinationsStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<HealthRecordIsar> _unfilteredHealthRecords = []; // Complete dataset for analytics calculations
  List<MedicationIsar> _unfilteredMedications = []; // Complete medications dataset
  List<TreatmentIsar> _unfilteredTreatments = []; // Complete treatments dataset
  List<VaccinationIsar> _unfilteredVaccinations = []; // Complete vaccinations dataset
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics

  List<HealthRecordIsar> _filteredHealthRecords = []; // Filtered dataset for UI display
  List<TreatmentIsar> _filteredTreatments = []; // Filtered treatments for UI display
  List<VaccinationIsar> _filteredVaccinations = []; // Filtered vaccinations for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  HealthAnalyticsResult _analyticsResult = HealthAnalyticsResult.empty;

  // Filter state management - decoupled from UI
  HealthFilterState _currentFilters = HealthFilterState.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered health records for UI display
  /// This is what the HealthRecordsTab should show
  List<HealthRecordIsar> get healthRecords => List.unmodifiable(_filteredHealthRecords);

  /// Returns the filtered treatments for UI display
  /// This is what the TreatmentsTab should show
  List<TreatmentIsar> get treatments => List.unmodifiable(_filteredTreatments);

  /// Returns the filtered vaccinations for UI display
  /// This is what the VaccinationsTab should show
  List<VaccinationIsar> get vaccinations => List.unmodifiable(_filteredVaccinations);

  /// Returns the complete unfiltered health records for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<HealthRecordIsar> get unfilteredHealthRecords => List.unmodifiable(_unfilteredHealthRecords);

  /// Returns the complete unfiltered treatments for analytics
  List<TreatmentIsar> get unfilteredTreatments => List.unmodifiable(_unfilteredTreatments);

  /// Returns the complete unfiltered vaccinations for analytics
  List<VaccinationIsar> get unfilteredVaccinations => List.unmodifiable(_unfilteredVaccinations);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  HealthAnalyticsResult get analytics => _analyticsResult;

  // Filter state access
  HealthFilterState get currentFilters => _currentFilters;

  // Convenience getters for backward compatibility with UI
  int get totalHealthRecords => _analyticsResult.totalHealthRecords;
  int get activeRecords => _analyticsResult.activeRecords;
  int get resolvedRecords => _analyticsResult.resolvedRecords;
  int get treatmentRecords => _analyticsResult.treatmentRecords;
  int get vaccinationRecords => _analyticsResult.vaccinationRecords;
  int get medicationRecords => _analyticsResult.medicationRecords;
  double get averageHealthScore => _analyticsResult.averageHealthScore;
  double get totalHealthCosts => _analyticsResult.totalHealthCosts;
  Map<String, int> get recordsByType => _analyticsResult.recordTypeDistribution;
  Map<String, int> get recordsByStatus => _analyticsResult.statusDistribution;
  Map<String, int> get conditionDistribution => _analyticsResult.conditionDistribution;
  int get cattleWithRecentTreatments => _analyticsResult.cattleWithRecentTreatments;
  int get overdueVaccinations => _analyticsResult.overdueVaccinations;
  List<String> get chronicConditions => _analyticsResult.chronicConditions;
  double get treatmentSuccessRate => _analyticsResult.treatmentSuccessRate;

  // Additional getters for analytics tabs
  int get completedRecords => _analyticsResult.resolvedRecords;
  List<CattleIsar> get cattle => _unfilteredCattle;

  // Constructor
  HealthController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates using Individual Streams Pattern
  /// Following the cattle module pattern - NO StreamZip
  void _initializeStreamListeners() {
    _logger.info('Initializing individual stream listeners (cattle module pattern)...');

    // Health Records stream
    _healthRecordsStreamSubscription = _isar.healthRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((records) {
      _logger.info('Health records stream: Received $records.length health records');
      _unfilteredHealthRecords = records;
      _updateAnalytics(); // Update analytics when health records change
      _updateFilteredDataAndNotify();
    }, onError: (error) {
      _logger.severe('Health records stream error: $error');
    });

    // Medications stream
    _medicationsStreamSubscription = _isar.medicationIsars.where()
        .watch(fireImmediately: true)
        .listen((medications) {
      _logger.info('Medications stream: Received $medications.length medications');
      _unfilteredMedications = medications;
      _updateAnalytics();
    }, onError: (error) {
      _logger.severe('Medications stream error: $error');
    });

    // Treatments stream
    _treatmentsStreamSubscription = _isar.treatmentIsars.where()
        .watch(fireImmediately: true)
        .listen((treatments) {
      _logger.info('Treatments stream: Received $treatments.length treatments');
      _unfilteredTreatments = treatments;
      _updateAnalytics(); // Update analytics when treatments change
      _updateFilteredDataAndNotify();
    }, onError: (error) {
      debugPrint('❌ Treatments stream error: $error');
    });

    // Vaccinations stream
    _vaccinationsStreamSubscription = _isar.vaccinationIsars.where()
        .watch(fireImmediately: true)
        .listen((vaccinations) {
      debugPrint('🔄 VACCINATIONS STREAM: Received $vaccinations.length vaccinations');
      _unfilteredVaccinations = vaccinations;
      _updateAnalytics(); // Update analytics when vaccinations change
      _updateFilteredDataAndNotify();
    }, onError: (error) {
      debugPrint('❌ Vaccinations stream error: $error');
    });

    // Cattle stream (for relationships)
    _cattleStreamSubscription = _cattleRepository.watchAllCattle()
        .listen((cattle) {
      debugPrint('🔄 CATTLE STREAM: Received $cattle.length cattle records');
      _unfilteredCattle = cattle;
      _updateAnalytics();
    }, onError: (error) {
      debugPrint('❌ Cattle stream error: $error');
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredHealthRecords = _unfilteredHealthRecords;
    _filteredTreatments = _unfilteredTreatments;
    _filteredVaccinations = _unfilteredVaccinations;
    _hasActiveFilters = false;

    debugPrint('✅ HEALTH CONTROLLER: Individual stream listeners initialized');
  }

  /// Update filtered data and notify listeners (following cattle module pattern)
  void _updateFilteredDataAndNotify() {
    try {
      debugPrint('📊 HEALTH CONTROLLER: Updating filtered data and UI');

      // Update filtered data if no filters are active
      if (!_hasActiveFilters) {
        _filteredHealthRecords = List.from(_unfilteredHealthRecords);
        _filteredTreatments = List.from(_unfilteredTreatments);
        _filteredVaccinations = List.from(_unfilteredVaccinations);
        _setState(ControllerState.loaded);
        debugPrint('🔄 HEALTH CONTROLLER: Updated filtered data - Health: $_filteredHealthRecords.length, Vaccinations: $_filteredVaccinations.length');
      }

      debugPrint('🔔 HEALTH CONTROLLER: Calling notifyListeners() - UI should update now');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error updating filtered data: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update data: $e';
      notifyListeners();
    }
  }

  /// Update analytics calculations (following cattle module pattern)
  void _updateAnalytics() {
    try {
      debugPrint('📊 HEALTH CONTROLLER: Updating analytics...');
      debugPrint('📊 HEALTH CONTROLLER: Data counts - Health: $_unfilteredHealthRecords.length, Treatments: $_unfilteredTreatments.length, Vaccinations: $_unfilteredVaccinations.length, Medications: $_unfilteredMedications.length');

      // Calculate analytics using unfiltered data
      if (_unfilteredHealthRecords.isEmpty && _unfilteredMedications.isEmpty &&
          _unfilteredTreatments.isEmpty && _unfilteredVaccinations.isEmpty) {
        _analyticsResult = HealthAnalyticsResult.empty;
        debugPrint('📊 HEALTH CONTROLLER: Analytics set to empty (no data)');
      } else {
        _calculateAnalytics();
        debugPrint('📊 HEALTH CONTROLLER: Analytics calculated - Total Health Records: $_analyticsResult.totalHealthRecords, Vaccinations: $_analyticsResult.vaccinationRecords');
      }
    } catch (e) {
      debugPrint('❌ Error updating analytics: $e');
    }
  }

  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = HealthAnalyticsService.calculate(
      _unfilteredHealthRecords, // Use unfiltered data for accurate analytics
      _unfilteredMedications,
      _unfilteredTreatments,
      _unfilteredVaccinations,
      _unfilteredCattle,
    );
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(HealthFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredHealthRecords = List.from(_unfilteredHealthRecords);
      _filteredTreatments = List.from(_unfilteredTreatments);
      _filteredVaccinations = List.from(_unfilteredVaccinations);
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? recordType,
    String? status,
  }) {
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      recordType: recordType,
      status: status,
    );
    applyFilters(newFilters);
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(HealthFilterState.empty);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<HealthRecordIsar> filteredList) {
    try {
      // Update only the filtered dataset for UI display
      _filteredHealthRecords = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(HealthFilterState filterState) {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.healthRecordIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .conditionContains(searchTerm, caseSensitive: false)
          .or()
          .notesContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filterState.startDate != null) {
      currentQuery = currentQuery.filter().dateGreaterThan(filterState.startDate!);
    }
    if (filterState.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().dateLessThan(inclusiveEndDate);
    }

    // Apply record type filter
    if (filterState.recordType?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().recordTypeEqualTo(filterState.recordType);
    }

    // Apply status filter
    if (filterState.status?.isNotEmpty == true && filterState.status != 'All') {
      currentQuery = currentQuery.filter().statusEqualTo(filterState.status);
    }

    // Apply cattle filter
    if (filterState.cattleId?.isNotEmpty == true && filterState.cattleId != 'All') {
      currentQuery = currentQuery.filter().cattleTagIdEqualTo(filterState.cattleId);
    }

    // Apply sorting at database level for optimal performance
    currentQuery = currentQuery.sortByDateDesc(); // Default sort by date (newest first)

    return currentQuery;
  }



  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new health record - only updates database, stream handles UI update
  Future<void> addHealthRecord(HealthRecordIsar record) async {
    await _healthRepository.saveHealthRecord(record);
    // Stream will handle the UI update automatically
  }

  /// Add new treatment record - only updates database, stream handles UI update
  Future<void> addTreatmentRecord(TreatmentIsar record) async {
    await _healthRepository.saveTreatment(record);
    // Stream will handle the UI update automatically
  }

  /// Add new vaccination record - only updates database, stream handles UI update
  Future<void> addVaccinationRecord(VaccinationIsar record) async {
    await _healthRepository.saveVaccination(record);
    // Stream will handle the UI update automatically
  }

  /// Update treatment record
  Future<void> updateTreatmentRecord(TreatmentIsar record) async {
    await _healthRepository.saveTreatment(record);
    // Stream will handle the UI update automatically
  }

  /// Update vaccination record
  Future<void> updateVaccinationRecord(VaccinationIsar record) async {
    debugPrint('🔄 HEALTH CONTROLLER: Starting updateVaccinationRecord for record $record.id');
    debugPrint('📊 HEALTH CONTROLLER: Current vaccinations count before update: $_unfilteredVaccinations.length');

    await _healthRepository.saveVaccination(record);

    debugPrint('✅ HEALTH CONTROLLER: Repository update completed for vaccination $record.id');
    debugPrint('📊 HEALTH CONTROLLER: Current vaccinations count after update: $_unfilteredVaccinations.length');
    // Stream will handle the UI update automatically
  }

  /// Delete treatment record
  Future<void> deleteTreatmentRecord(int recordId) async {
    await _healthRepository.deleteTreatment(recordId);
    // Stream will handle the UI update automatically
  }

  /// Delete vaccination record
  Future<void> deleteVaccinationRecord(int recordId) async {
    debugPrint('🗑️ HEALTH CONTROLLER: Starting deleteVaccinationRecord for record $recordId');
    debugPrint('📊 HEALTH CONTROLLER: Current vaccinations count before delete: $_unfilteredVaccinations.length');

    await _healthRepository.deleteVaccination(recordId);

    debugPrint('✅ HEALTH CONTROLLER: Repository delete completed for vaccination $recordId');
    debugPrint('📊 HEALTH CONTROLLER: Current vaccinations count after delete: $_unfilteredVaccinations.length');
    // Stream will handle the UI update automatically
  }

  /// Get unique conditions for filter dropdown
  List<String> getUniqueConditions() {
    final conditions = <String>{};
    for (final record in _unfilteredHealthRecords) {
      if (record.condition?.isNotEmpty == true) {
        conditions.add(record.condition!);
      }
    }
    for (final record in _unfilteredTreatments) {
      if (record.condition?.isNotEmpty == true) {
        conditions.add(record.condition!);
      }
    }
    return conditions.toList()..sort();
  }

  /// Get unique veterinarians for filter dropdown
  List<String> getUniqueVeterinarians() {
    final vets = <String>{};
    for (final record in _unfilteredHealthRecords) {
      if (record.veterinarian?.isNotEmpty == true) {
        vets.add(record.veterinarian!);
      }
    }
    for (final record in _unfilteredTreatments) {
      if (record.veterinarian?.isNotEmpty == true) {
        vets.add(record.veterinarian!);
      }
    }
    return vets.toList()..sort();
  }

  /// Get unique vaccine names for filter dropdown
  List<String> getUniqueVaccineNames() {
    final vaccines = <String>{};
    for (final record in _unfilteredVaccinations) {
      if (record.vaccineName?.isNotEmpty == true) {
        vaccines.add(record.vaccineName!);
      }
    }
    return vaccines.toList()..sort();
  }

  /// Get unique manufacturers for filter dropdown
  List<String> getUniqueManufacturers() {
    final manufacturers = <String>{};
    for (final record in _unfilteredVaccinations) {
      if (record.manufacturer?.isNotEmpty == true) {
        manufacturers.add(record.manufacturer!);
      }
    }
    return manufacturers.toList()..sort();
  }

  /// Update health record - only updates database, stream handles UI update
  Future<void> updateHealthRecord(HealthRecordIsar updatedRecord) async {
    debugPrint('🔄 HEALTH CONTROLLER: Starting updateHealthRecord for record $updatedRecord.id');
    debugPrint('📊 HEALTH CONTROLLER: Current health records count before update: $_unfilteredHealthRecords.length');

    await _healthRepository.saveHealthRecord(updatedRecord);

    debugPrint('✅ HEALTH CONTROLLER: Repository update completed for record $updatedRecord.id');
    debugPrint('📊 HEALTH CONTROLLER: Current health records count after update: $_unfilteredHealthRecords.length');
    // Stream will handle the UI update automatically
  }

  /// Delete health record - only updates database, stream handles UI update
  Future<void> deleteHealthRecord(int recordId) async {
    debugPrint('🗑️ HEALTH CONTROLLER: Starting deleteHealthRecord for record $recordId');
    debugPrint('📊 HEALTH CONTROLLER: Current health records count before delete: $_unfilteredHealthRecords.length');

    await _healthRepository.deleteHealthRecord(recordId);

    debugPrint('✅ HEALTH CONTROLLER: Repository delete completed for record $recordId');
    debugPrint('📊 HEALTH CONTROLLER: Current health records count after delete: $_unfilteredHealthRecords.length');
    // Stream will handle the UI update automatically
  }

  /// Refresh all data - compatible with analytics tab refresh functionality
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      if (_unfilteredHealthRecords.isNotEmpty || _unfilteredMedications.isNotEmpty ||
          _unfilteredTreatments.isNotEmpty || _unfilteredVaccinations.isNotEmpty) {
        _calculateAnalytics();
      }

      // Notify listeners of the refresh
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing health data: $e\n$stackTrace');
      throw Exception('Failed to refresh health data: ${e.toString()}');
    }
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleTagId) {
    if (cattleTagId == null || cattleTagId.isEmpty) {
      debugPrint('🔍 HEALTH CONTROLLER: Null or empty cattleTagId provided');
      return null;
    }
    try {
      debugPrint('🔍 HEALTH CONTROLLER: Looking for cattle with tagId: "$cattleTagId"');
      final cattle = _unfilteredCattle.firstWhere(
        (cattle) => cattle.tagId == cattleTagId,
      );
      debugPrint('✅ HEALTH CONTROLLER: Found cattle: $cattle.name ($cattle.tagId)');
      return cattle;
    } catch (e) {
      debugPrint('❌ HEALTH CONTROLLER: No cattle found with tagId: "$cattleTagId"');
      debugPrint('🔍 HEALTH CONTROLLER: Available cattle: ${_unfilteredCattle.map((c) => '${c.name}($c.tagId)').join(', ')}');
      return null;
    }
  }

  String getCattleName(String? cattleBusinessId) {
    final cattle = getCattle(cattleBusinessId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  // _setError method removed - unused

  @override
  void dispose() {
    // Cancel all individual stream subscriptions
    _healthRecordsStreamSubscription?.cancel();
    _medicationsStreamSubscription?.cancel();
    _treatmentsStreamSubscription?.cancel();
    _vaccinationsStreamSubscription?.cancel();
    _cattleStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
