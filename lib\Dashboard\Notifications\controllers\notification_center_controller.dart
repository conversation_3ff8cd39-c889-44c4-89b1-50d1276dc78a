import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:logging/logging.dart';

import '../services/notification_service.dart';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';
import '../models/notification_priority.dart';

/// Controller for managing notification center UI state
class NotificationCenterController extends ChangeNotifier {
  final Logger _logger = Logger('NotificationCenterController');
  final NotificationService _service = GetIt.instance<NotificationService>();

  // State variables
  List<NotificationIsar> _notifications = [];
  List<NotificationIsar> _filteredNotifications = [];
  bool _isLoading = false;
  String? _error;
  
  // Filter state
  String? _selectedCategory;
  NotificationStatus? _selectedStatus;
  NotificationPriority? _selectedPriority;
  String? _searchQuery;
  DateTime? _fromDate;
  DateTime? _toDate;
  String? _selectedCattleId;
  
  // Selection state
  final Set<String> _selectedNotificationIds = {};
  bool _isSelectionMode = false;
  
  // Pagination state
  final int _pageSize = 20;
  bool _hasMoreData = true;

  // Getters
  List<NotificationIsar> get notifications => _filteredNotifications;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get selectedCategory => _selectedCategory;
  NotificationStatus? get selectedStatus => _selectedStatus;
  NotificationPriority? get selectedPriority => _selectedPriority;
  String? get searchQuery => _searchQuery;
  DateTime? get fromDate => _fromDate;
  DateTime? get toDate => _toDate;
  String? get selectedCattleId => _selectedCattleId;
  Set<String> get selectedNotificationIds => _selectedNotificationIds;
  bool get isSelectionMode => _isSelectionMode;
  bool get hasMoreData => _hasMoreData;
  int get totalCount => _notifications.length;
  int get selectedCount => _selectedNotificationIds.length;

  /// Initialize the controller
  Future<void> initialize() async {
    try {
      _setLoading(true);
      await loadNotifications();
      
      // Listen to notification changes
      _service.onNotificationChanges.listen((_) {
        refreshNotifications();
      });
      
      _logger.info('NotificationCenterController initialized');
    } catch (e) {
      _setError('Failed to initialize notification center: $e');
      _logger.severe('Error initializing NotificationCenterController: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load notifications with current filters
  Future<void> loadNotifications({bool append = false}) async {
    try {
      if (!append) {
        _setLoading(true);
        _clearError();
        _hasMoreData = true;
      }
      
      final filter = _buildCurrentFilter();
      final notifications = await _service.getNotifications(filter: filter);
      
      if (append) {
        _notifications.addAll(notifications);
      } else {
        _notifications = notifications;
      }
      
      _applyLocalFilters();
      _hasMoreData = notifications.length == _pageSize;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load notifications: $e');
      _logger.severe('Error loading notifications: $e');
    } finally {
      if (!append) {
        _setLoading(false);
      }
    }
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (!_hasMoreData || _isLoading) return;

    await loadNotifications(append: true);
  }

  /// Refresh notifications
  Future<void> refreshNotifications() async {
    await loadNotifications();
  }

  /// Apply category filter
  Future<void> filterByCategory(String? category) async {
    _selectedCategory = category;
    await loadNotifications();
  }

  /// Apply status filter
  Future<void> filterByStatus(NotificationStatus? status) async {
    _selectedStatus = status;
    await loadNotifications();
  }

  /// Apply priority filter
  Future<void> filterByPriority(NotificationPriority? priority) async {
    _selectedPriority = priority;
    await loadNotifications();
  }

  /// Apply search query
  Future<void> search(String? query) async {
    _searchQuery = query;
    await loadNotifications();
  }

  /// Apply date range filter
  Future<void> filterByDateRange(DateTime? fromDate, DateTime? toDate) async {
    _fromDate = fromDate;
    _toDate = toDate;
    await loadNotifications();
  }

  /// Apply cattle filter
  Future<void> filterByCattle(String? cattleId) async {
    _selectedCattleId = cattleId;
    await loadNotifications();
  }

  /// Clear all filters
  Future<void> clearAllFilters() async {
    _selectedCategory = null;
    _selectedStatus = null;
    _selectedPriority = null;
    _searchQuery = null;
    _fromDate = null;
    _toDate = null;
    _selectedCattleId = null;
    await loadNotifications();
  }

  /// Toggle selection mode
  void toggleSelectionMode() {
    _isSelectionMode = !_isSelectionMode;
    if (!_isSelectionMode) {
      _selectedNotificationIds.clear();
    }
    notifyListeners();
  }

  /// Select notification
  void selectNotification(String notificationId) {
    if (_selectedNotificationIds.contains(notificationId)) {
      _selectedNotificationIds.remove(notificationId);
    } else {
      _selectedNotificationIds.add(notificationId);
    }
    notifyListeners();
  }

  /// Select all notifications
  void selectAll() {
    _selectedNotificationIds.clear();
    _selectedNotificationIds.addAll(_filteredNotifications.map((n) => n.businessId!));
    notifyListeners();
  }

  /// Clear selection
  void clearSelection() {
    _selectedNotificationIds.clear();
    notifyListeners();
  }

  /// Mark selected notifications as read
  Future<void> markSelectedAsRead() async {
    if (_selectedNotificationIds.isEmpty) return;
    
    try {
      await _service.markMultipleAsRead(_selectedNotificationIds.toList());
      _clearSelection();
      await refreshNotifications();
    } catch (e) {
      _setError('Failed to mark selected as read: $e');
      _logger.severe('Error marking selected as read: $e');
    }
  }

  /// Delete selected notifications
  Future<void> deleteSelected() async {
    if (_selectedNotificationIds.isEmpty) return;
    
    try {
      await _service.deleteMultiple(_selectedNotificationIds.toList());
      _clearSelection();
      await refreshNotifications();
    } catch (e) {
      _setError('Failed to delete selected notifications: $e');
      _logger.severe('Error deleting selected notifications: $e');
    }
  }

  /// Archive selected notifications
  Future<void> archiveSelected() async {
    if (_selectedNotificationIds.isEmpty) return;
    
    try {
      for (final id in _selectedNotificationIds) {
        await _service.archiveNotification(id);
      }
      _clearSelection();
      await refreshNotifications();
    } catch (e) {
      _setError('Failed to archive selected notifications: $e');
      _logger.severe('Error archiving selected notifications: $e');
    }
  }

  /// Group notifications by category
  Map<String, List<NotificationIsar>> getNotificationsByCategory() {
    final grouped = <String, List<NotificationIsar>>{};
    
    for (final notification in _filteredNotifications) {
      final category = notification.category ?? 'Other';
      grouped.putIfAbsent(category, () => []).add(notification);
    }
    
    return grouped;
  }

  /// Group notifications by date
  Map<String, List<NotificationIsar>> getNotificationsByDate() {
    final grouped = <String, List<NotificationIsar>>{};
    
    for (final notification in _filteredNotifications) {
      final date = notification.createdAt?.toIso8601String().split('T')[0] ?? 'Unknown';
      grouped.putIfAbsent(date, () => []).add(notification);
    }
    
    return grouped;
  }

  /// Build current filter from state
  NotificationFilter _buildCurrentFilter() {
    return NotificationFilter(
      status: _selectedStatus,
      category: _selectedCategory,
      priority: _selectedPriority,
      cattleId: _selectedCattleId,
      fromDate: _fromDate,
      toDate: _toDate,
      searchQuery: _searchQuery,
    );
  }

  /// Apply local filters (for search and other client-side filtering)
  void _applyLocalFilters() {
    _filteredNotifications = _notifications;
    
    // Apply search query if present
    if (_searchQuery != null && _searchQuery!.isNotEmpty) {
      _filteredNotifications = _filteredNotifications.where((notification) {
        final query = _searchQuery!.toLowerCase();
        return (notification.title?.toLowerCase().contains(query) ?? false) ||
               (notification.message?.toLowerCase().contains(query) ?? false);
      }).toList();
    }
  }

  /// Clear selection
  void _clearSelection() {
    _selectedNotificationIds.clear();
    _isSelectionMode = false;
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error state
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error state
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
    _logger.info('NotificationCenterController disposed');
  }
}