import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:isar/isar.dart';
import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

import '../../../services/database/isar_service.dart';
import '../models/notification_operation.dart';
import '../models/notification_isar.dart';
import '../models/notification_status.dart';
import 'notification_repository.dart';

/// Service for managing notification operations when offline
class OfflineNotificationService {
  final Logger _logger = Logger('OfflineNotificationService');
  final IsarService _isarService;
  final NotificationRepository _notificationRepository;
  final Connectivity _connectivity = Connectivity();
  final Uuid _uuid = const Uuid();

  bool _isOnline = true;
  bool _isSyncing = false;

  OfflineNotificationService(this._isarService, this._notificationRepository) {
    _initializeConnectivityMonitoring();
  }

  /// Initialize connectivity monitoring
  void _initializeConnectivityMonitoring() {
    _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
      final wasOffline = !_isOnline;
      _isOnline = results.isNotEmpty && !results.contains(ConnectivityResult.none);
      
      if (wasOffline && _isOnline) {
        _logger.info('Connection restored, starting sync');
        syncPendingOperations();
      }
    });
  }

  /// Queue an operation for later processing
  Future<void> queueOperation(NotificationOperationType type, {
    String? notificationId,
    Map<String, dynamic>? data,
  }) async {
    try {
      final operation = NotificationOperation()
        ..operationId = _uuid.v4()
        ..type = type
        ..notificationId = notificationId
        ..operationData = data != null ? jsonEncode(data) : null
        ..createdAt = DateTime.now()
        ..scheduledFor = DateTime.now();

      await _isarService.isar.writeTxn(() async {
        await _isarService.isar.notificationOperations.put(operation);
      });

      _logger.info('Queued operation: ${type.name} for notification: $notificationId');
    } catch (e) {
      _logger.severe('Error queuing operation: $e');
      rethrow;
    }
  }

  /// Get all pending operations
  Future<List<NotificationOperation>> getPendingOperations() async {
    try {
      return await _isarService.isar.notificationOperations
          .filter()
          .isProcessedEqualTo(false)
          .sortByCreatedAt()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting pending operations: $e');
      rethrow;
    }
  }

  /// Sync all pending operations
  Future<void> syncPendingOperations() async {
    if (_isSyncing || !_isOnline) return;

    try {
      _isSyncing = true;
      final pendingOps = await getPendingOperations();
      
      _logger.info('Syncing ${pendingOps.length} pending operations');

      for (final operation in pendingOps) {
        await _processOperation(operation);
      }

      _logger.info('Sync completed successfully');
    } catch (e) {
      _logger.severe('Error during sync: $e');
    } finally {
      _isSyncing = false;
    }
  }

  /// Process a single operation
  Future<void> _processOperation(NotificationOperation operation) async {
    try {
      bool success = false;

      switch (operation.type) {
        case NotificationOperationType.create:
          success = await _processCreateOperation(operation);
          break;
        case NotificationOperationType.update:
          success = await _processUpdateOperation(operation);
          break;
        case NotificationOperationType.delete:
          success = await _processDeleteOperation(operation);
          break;
        case NotificationOperationType.markAsRead:
          success = await _processMarkAsReadOperation(operation);
          break;
        case NotificationOperationType.markAsUnread:
          success = await _processMarkAsUnreadOperation(operation);
          break;
      }

      if (success) {
        await _markOperationAsProcessed(operation);
      } else {
        await _handleOperationFailure(operation);
      }
    } catch (e) {
      _logger.severe('Error processing operation ${operation.operationId}: $e');
      await _handleOperationFailure(operation, e.toString());
    }
  }

  /// Process create operation
  Future<bool> _processCreateOperation(NotificationOperation operation) async {
    if (operation.operationData == null) return false;

    try {
      final data = jsonDecode(operation.operationData!);
      final notification = NotificationIsar.fromMap(data);
      await _notificationRepository.saveNotification(notification);
      return true;
    } catch (e) {
      _logger.severe('Error processing create operation: $e');
      return false;
    }
  }

  /// Process update operation
  Future<bool> _processUpdateOperation(NotificationOperation operation) async {
    if (operation.notificationId == null || operation.operationData == null) return false;

    try {
      final data = jsonDecode(operation.operationData!);
      final notification = NotificationIsar.fromMap(data);
      await _notificationRepository.saveNotification(notification);
      return true;
    } catch (e) {
      _logger.severe('Error processing update operation: $e');
      return false;
    }
  }

  /// Process delete operation
  Future<bool> _processDeleteOperation(NotificationOperation operation) async {
    if (operation.notificationId == null) return false;

    try {
      await _notificationRepository.deleteNotification(operation.notificationId!);
      return true;
    } catch (e) {
      _logger.severe('Error processing delete operation: $e');
      return false;
    }
  }

  /// Process mark as read operation
  Future<bool> _processMarkAsReadOperation(NotificationOperation operation) async {
    if (operation.notificationId == null) return false;

    try {
      final notification = await _notificationRepository.getNotificationById(operation.notificationId!);
      if (notification != null) {
        notification.status = NotificationStatus.read;
        notification.readAt = DateTime.now();
        await _notificationRepository.saveNotification(notification);
      }
      return true;
    } catch (e) {
      _logger.severe('Error processing mark as read operation: $e');
      return false;
    }
  }

  /// Process mark as unread operation
  Future<bool> _processMarkAsUnreadOperation(NotificationOperation operation) async {
    if (operation.notificationId == null) return false;

    try {
      final notification = await _notificationRepository.getNotificationById(operation.notificationId!);
      if (notification != null) {
        notification.status = NotificationStatus.unread;
        notification.readAt = null;
        await _notificationRepository.saveNotification(notification);
      }
      return true;
    } catch (e) {
      _logger.severe('Error processing mark as unread operation: $e');
      return false;
    }
  }

  /// Mark operation as processed
  Future<void> _markOperationAsProcessed(NotificationOperation operation) async {
    try {
      operation.isProcessed = true;
      operation.processedAt = DateTime.now();

      await _isarService.isar.writeTxn(() async {
        await _isarService.isar.notificationOperations.put(operation);
      });

      _logger.info('Marked operation as processed: ${operation.operationId}');
    } catch (e) {
      _logger.severe('Error marking operation as processed: $e');
    }
  }

  /// Handle operation failure
  Future<void> _handleOperationFailure(NotificationOperation operation, [String? errorMessage]) async {
    try {
      operation.retryCount++;
      operation.errorMessage = errorMessage;

      if (operation.retryCount >= operation.maxRetries) {
        operation.isProcessed = true;
        operation.processedAt = DateTime.now();
        _logger.warning('Operation failed after max retries: ${operation.operationId}');
      }

      await _isarService.isar.writeTxn(() async {
        await _isarService.isar.notificationOperations.put(operation);
      });
    } catch (e) {
      _logger.severe('Error handling operation failure: $e');
    }
  }

  /// Check if there are unsynced changes
  bool get hasUnsyncedChanges => getPendingOperations().then((ops) => ops.isNotEmpty) as bool;

  /// Get last sync time
  DateTime? get lastSyncTime {
    // This would typically be stored in settings or preferences
    return null;
  }

  /// Queue create notification operation
  Future<void> queueCreateNotification(dynamic request) async {
    await queueOperation(
      NotificationOperationType.create,
      data: request.toJson(),
    );
  }

  /// Queue mark as read operation
  Future<void> queueMarkAsRead(String notificationId) async {
    await queueOperation(
      NotificationOperationType.markAsRead,
      notificationId: notificationId,
    );
  }

  /// Queue mark as actioned operation
  Future<void> queueMarkAsActioned(String notificationId) async {
    await queueOperation(
      NotificationOperationType.update,
      notificationId: notificationId,
      data: {'status': 'actioned'},
    );
  }

  /// Queue archive operation
  Future<void> queueArchive(String notificationId) async {
    await queueOperation(
      NotificationOperationType.update,
      notificationId: notificationId,
      data: {'status': 'archived'},
    );
  }

  /// Queue delete operation
  Future<void> queueDelete(String notificationId) async {
    await queueOperation(
      NotificationOperationType.delete,
      notificationId: notificationId,
    );
  }

  /// Initialize the service
  Future<void> initialize() async {
    _logger.info('OfflineNotificationService initialized');
  }

  /// Dispose the service
  void dispose() {
    _logger.info('OfflineNotificationService disposed');
  }

  /// Clean up old processed operations
  Future<void> cleanupOldOperations({int daysToKeep = 7}) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      
      await _isarService.isar.writeTxn(() async {
        await _isarService.isar.notificationOperations
            .filter()
            .isProcessedEqualTo(true)
            .processedAtLessThan(cutoffDate)
            .deleteAll();
      });

      _logger.info('Cleaned up old operations older than $daysToKeep days');
    } catch (e) {
      _logger.severe('Error cleaning up old operations: $e');
    }
  }
}