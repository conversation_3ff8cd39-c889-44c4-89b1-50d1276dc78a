import 'package:isar/isar.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../models/user_isar.dart';
import '../models/user_session_isar.dart';
import '../models/user_settings_isar.dart';

/// Repository for user account operations with comprehensive CRUD functionality
class UserRepository {
  final Logger _logger = Logger('UserRepository');
  final IsarService _isarService = GetIt.instance<IsarService>();

  // User CRUD operations
  
  /// Create a new user
  Future<UserIsar> createUser(UserIsar user) async {
    try {
      await _isarService.isar.writeTxn(() async {
        await _isarService.userIsars.put(user);
      });
      _logger.info('User created: $user.email');
      return user;
    } catch (e) {
      _logger.severe('Error creating user: $e');
      throw DatabaseException('Failed to create user', e.toString());
    }
  }

  /// Get user by business ID
  Future<UserIsar?> getUserByBusinessId(String businessId) async {
    try {
      return await _isarService.userIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting user by business ID: $e');
      throw DatabaseException('Failed to get user', e.toString());
    }
  }

  /// Get user by email
  Future<UserIsar?> getUserByEmail(String email) async {
    try {
      return await _isarService.userIsars
          .filter()
          .emailEqualTo(email.toLowerCase())
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting user by email: $e');
      throw DatabaseException('Failed to get user', e.toString());
    }
  }

  /// Get user by username
  Future<UserIsar?> getUserByUsername(String username) async {
    try {
      return await _isarService.userIsars
          .filter()
          .usernameEqualTo(username.toLowerCase())
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting user by username: $e');
      throw DatabaseException('Failed to get user', e.toString());
    }
  }

  /// Update user
  Future<UserIsar> updateUser(UserIsar user) async {
    try {
      user.updatedAt = DateTime.now();
      await _isarService.isar.writeTxn(() async {
        await _isarService.userIsars.put(user);
      });
      _logger.info('User updated: $user.email');
      return user;
    } catch (e) {
      _logger.severe('Error updating user: $e');
      throw DatabaseException('Failed to update user', e.toString());
    }
  }

  /// Delete user (soft delete by deactivating)
  Future<void> deactivateUser(String businessId) async {
    try {
      final user = await getUserByBusinessId(businessId);
      if (user != null) {
        user.isActive = false;
        user.updatedAt = DateTime.now();
        await updateUser(user);
        _logger.info('User deactivated: $user.email');
      }
    } catch (e) {
      _logger.severe('Error deactivating user: $e');
      throw DatabaseException('Failed to deactivate user', e.toString());
    }
  }

  /// Get all active users
  Future<List<UserIsar>> getAllActiveUsers() async {
    try {
      return await _isarService.userIsars
          .filter()
          .isActiveEqualTo(true)
          .findAll();
    } catch (e) {
      _logger.severe('Error getting all active users: $e');
      throw DatabaseException('Failed to get users', e.toString());
    }
  }

  // User Session operations

  /// Create a new session
  Future<UserSessionIsar> createSession(UserSessionIsar session) async {
    try {
      await _isarService.isar.writeTxn(() async {
        await _isarService.userSessionIsars.put(session);
      });
      _logger.info('Session created for user: $session.userBusinessId');
      return session;
    } catch (e) {
      _logger.severe('Error creating session: $e');
      throw DatabaseException('Failed to create session', e.toString());
    }
  }

  /// Get session by token
  Future<UserSessionIsar?> getSessionByToken(String token) async {
    try {
      return await _isarService.userSessionIsars
          .filter()
          .sessionTokenEqualTo(token)
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting session by token: $e');
      throw DatabaseException('Failed to get session', e.toString());
    }
  }

  /// Update session
  Future<UserSessionIsar> updateSession(UserSessionIsar session) async {
    try {
      await _isarService.isar.writeTxn(() async {
        await _isarService.userSessionIsars.put(session);
      });
      return session;
    } catch (e) {
      _logger.severe('Error updating session: $e');
      throw DatabaseException('Failed to update session', e.toString());
    }
  }

  /// Get all active sessions for a user
  Future<List<UserSessionIsar>> getUserActiveSessions(String userBusinessId) async {
    try {
      return await _isarService.userSessionIsars
          .filter()
          .userBusinessIdEqualTo(userBusinessId)
          .isActiveEqualTo(true)
          .findAll();
    } catch (e) {
      _logger.severe('Error getting user sessions: $e');
      throw DatabaseException('Failed to get user sessions', e.toString());
    }
  }

  /// Invalidate all sessions for a user
  Future<void> invalidateAllUserSessions(String userBusinessId) async {
    try {
      final sessions = await getUserActiveSessions(userBusinessId);
      await _isarService.isar.writeTxn(() async {
        for (final session in sessions) {
          session.invalidate();
          await _isarService.userSessionIsars.put(session);
        }
      });
      _logger.info('All sessions invalidated for user: $userBusinessId');
    } catch (e) {
      _logger.severe('Error invalidating user sessions: $e');
      throw DatabaseException('Failed to invalidate sessions', e.toString());
    }
  }

  /// Clean up expired sessions
  Future<void> cleanupExpiredSessions() async {
    try {
      final now = DateTime.now();
      final expiredSessions = await _isarService.userSessionIsars
          .filter()
          .expiresAtLessThan(now)
          .or()
          .isActiveEqualTo(false)
          .findAll();

      await _isarService.isar.writeTxn(() async {
        for (final session in expiredSessions) {
          await _isarService.userSessionIsars.delete(session.id);
        }
      });

      _logger.info('Cleaned up $expiredSessions.length expired sessions');
    } catch (e) {
      _logger.severe('Error cleaning up expired sessions: $e');
      throw DatabaseException('Failed to cleanup sessions', e.toString());
    }
  }

  // User Settings operations

  /// Create user settings
  Future<UserSettingsIsar> createUserSettings(UserSettingsIsar settings) async {
    try {
      await _isarService.isar.writeTxn(() async {
        await _isarService.userSettingsIsars.put(settings);
      });
      _logger.info('User settings created for: $settings.userBusinessId');
      return settings;
    } catch (e) {
      _logger.severe('Error creating user settings: $e');
      throw DatabaseException('Failed to create user settings', e.toString());
    }
  }

  /// Get user settings by user business ID
  Future<UserSettingsIsar?> getUserSettings(String userBusinessId) async {
    try {
      return await _isarService.userSettingsIsars
          .filter()
          .userBusinessIdEqualTo(userBusinessId)
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting user settings: $e');
      throw DatabaseException('Failed to get user settings', e.toString());
    }
  }

  /// Update user settings
  Future<UserSettingsIsar> updateUserSettings(UserSettingsIsar settings) async {
    try {
      settings.updatedAt = DateTime.now();
      await _isarService.isar.writeTxn(() async {
        await _isarService.userSettingsIsars.put(settings);
      });
      _logger.info('User settings updated for: $settings.userBusinessId');
      return settings;
    } catch (e) {
      _logger.severe('Error updating user settings: $e');
      throw DatabaseException('Failed to update user settings', e.toString());
    }
  }

  // Search and query operations

  /// Search users by name or email
  Future<List<UserIsar>> searchUsers(String query) async {
    try {
      final lowerQuery = query.toLowerCase();
      return await _isarService.userIsars
          .filter()
          .isActiveEqualTo(true)
          .and()
          .group((q) => q
              .firstNameContains(lowerQuery, caseSensitive: false)
              .or()
              .lastNameContains(lowerQuery, caseSensitive: false)
              .or()
              .emailContains(lowerQuery, caseSensitive: false)
              .or()
              .usernameContains(lowerQuery, caseSensitive: false))
          .findAll();
    } catch (e) {
      _logger.severe('Error searching users: $e');
      throw DatabaseException('Failed to search users', e.toString());
    }
  }

  /// Get users with pagination
  Future<List<UserIsar>> getUsersPaginated({
    int offset = 0,
    int limit = 20,
    bool activeOnly = true,
  }) async {
    try {
      if (activeOnly) {
        return await _isarService.userIsars
            .filter()
            .isActiveEqualTo(true)
            .offset(offset)
            .limit(limit)
            .findAll();
      } else {
        return await _isarService.userIsars
            .where()
            .offset(offset)
            .limit(limit)
            .findAll();
      }
    } catch (e) {
      _logger.severe('Error getting paginated users: $e');
      throw DatabaseException('Failed to get users', e.toString());
    }
  }

  /// Get user count
  Future<int> getUserCount({bool activeOnly = true}) async {
    try {
      if (activeOnly) {
        return await _isarService.userIsars
            .filter()
            .isActiveEqualTo(true)
            .count();
      } else {
        return await _isarService.userIsars.count();
      }
    } catch (e) {
      _logger.severe('Error getting user count: $e');
      throw DatabaseException('Failed to get user count', e.toString());
    }
  }

  // Utility methods

  /// Check if email exists
  Future<bool> emailExists(String email) async {
    try {
      final user = await getUserByEmail(email);
      return user != null;
    } catch (e) {
      _logger.severe('Error checking email existence: $e');
      return false;
    }
  }

  /// Check if username exists
  Future<bool> usernameExists(String username) async {
    try {
      final user = await getUserByUsername(username);
      return user != null;
    } catch (e) {
      _logger.severe('Error checking username existence: $e');
      return false;
    }
  }
}
