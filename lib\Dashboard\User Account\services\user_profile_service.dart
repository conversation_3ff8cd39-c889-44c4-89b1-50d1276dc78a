import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/user_isar.dart';
import '../models/user_settings_isar.dart';
import '../../Farm Setup/models/farm_isar.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import 'user_repository.dart';
import 'auth_service.dart';

/// Service for managing user profile operations
class UserProfileService {
  static final UserProfileService _instance = UserProfileService._internal();
  factory UserProfileService() => _instance;
  UserProfileService._internal();

  final Logger _logger = Logger('UserProfileService');
  final UserRepository _userRepository = UserRepository();
  final AuthService _authService = AuthService();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  final ImagePicker _imagePicker = ImagePicker();

  /// Get current user profile
  UserIsar? get currentUser => _authService.currentUser;

  /// Update user profile information
  Future<ProfileResult> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? farmName,
    String? farmLocation,
    String? farmDescription,
  }) async {
    try {
      if (!_authService.isAuthenticated) {
        return ProfileResult.failure('User not authenticated');
      }

      final user = _authService.currentUser!;
      
      // Validate input
      if (firstName != null && firstName.trim().isEmpty) {
        return ProfileResult.failure('First name cannot be empty');
      }
      
      if (lastName != null && lastName.trim().isEmpty) {
        return ProfileResult.failure('Last name cannot be empty');
      }

      // Update user profile
      user.updateProfile(
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        farmName: farmName,
        farmLocation: farmLocation,
        farmDescription: farmDescription,
      );

      await _userRepository.updateUser(user);

      // If farm information was updated, sync with farm records
      if (farmName != null || farmLocation != null || farmDescription != null) {
        await _syncUserFarmInfo(user);
      }

      _logger.info('Profile updated for user: $user.email');
      return ProfileResult.success('Profile updated successfully');

    } catch (e) {
      _logger.severe('Error updating profile: $e');
      return ProfileResult.failure('Failed to update profile');
    }
  }

  /// Update profile picture
  Future<ProfileResult> updateProfilePicture({ImageSource? source}) async {
    try {
      if (!_authService.isAuthenticated) {
        return ProfileResult.failure('User not authenticated');
      }

      final user = _authService.currentUser!;
      
      // Pick image
      final XFile? image = await _imagePicker.pickImage(
        source: source ?? ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image == null) {
        return ProfileResult.failure('No image selected');
      }

      // For now, store the local path. In production, you'd upload to cloud storage
      user.profilePictureUrl = image.path;
      user.updatedAt = DateTime.now();

      await _userRepository.updateUser(user);

      _logger.info('Profile picture updated for user: $user.email');
      return ProfileResult.success('Profile picture updated successfully');

    } catch (e) {
      _logger.severe('Error updating profile picture: $e');
      return ProfileResult.failure('Failed to update profile picture');
    }
  }

  /// Remove profile picture
  Future<ProfileResult> removeProfilePicture() async {
    try {
      if (!_authService.isAuthenticated) {
        return ProfileResult.failure('User not authenticated');
      }

      final user = _authService.currentUser!;
      user.profilePictureUrl = null;
      user.updatedAt = DateTime.now();

      await _userRepository.updateUser(user);

      _logger.info('Profile picture removed for user: $user.email');
      return ProfileResult.success('Profile picture removed successfully');

    } catch (e) {
      _logger.severe('Error removing profile picture: $e');
      return ProfileResult.failure('Failed to remove profile picture');
    }
  }

  /// Get user settings
  Future<UserSettingsIsar?> getUserSettings() async {
    try {
      if (!_authService.isAuthenticated) {
        return null;
      }

      return await _userRepository.getUserSettings(_authService.currentUser!.businessId!);
    } catch (e) {
      _logger.severe('Error getting user settings: $e');
      return null;
    }
  }

  /// Update user settings
  Future<ProfileResult> updateSettings(UserSettingsIsar settings) async {
    try {
      if (!_authService.isAuthenticated) {
        return ProfileResult.failure('User not authenticated');
      }

      await _userRepository.updateUserSettings(settings);

      _logger.info('Settings updated for user: ${_authService.currentUser!.email}');
      return ProfileResult.success('Settings updated successfully');

    } catch (e) {
      _logger.severe('Error updating settings: $e');
      return ProfileResult.failure('Failed to update settings');
    }
  }

  /// Associate user with a farm
  Future<ProfileResult> associateWithFarm(String farmBusinessId) async {
    try {
      if (!_authService.isAuthenticated) {
        return ProfileResult.failure('User not authenticated');
      }

      final user = _authService.currentUser!;
      final farms = await _farmSetupRepository.getAllFarms();
      final farm = farms.firstWhere(
        (f) => f.farmBusinessId == farmBusinessId,
        orElse: () => throw Exception('Farm not found'),
      );

      // Update the user's farm information
      user.updateProfile(
        farmName: farm.name,
        farmLocation: farm.location,
        farmDescription: null, // FarmIsar doesn't have description field
      );

      await _userRepository.updateUser(user);

      _logger.info('User $user.email associated with farm: $farm.name');
      return ProfileResult.success('Successfully associated with farm');

    } catch (e) {
      _logger.severe('Error associating with farm: $e');
      return ProfileResult.failure('Failed to associate with farm');
    }
  }

  /// Create a new farm for the user
  Future<ProfileResult> createUserFarm({
    required String farmName,
    required String location,
    String? description,
    String? contactPerson,
    String? contactPhone,
    String? contactEmail,
  }) async {
    try {
      if (!_authService.isAuthenticated) {
        return ProfileResult.failure('User not authenticated');
      }

      final user = _authService.currentUser!;
      final now = DateTime.now();

      // Create new farm using the correct FarmIsar.create constructor
      final farm = FarmIsar.create(
        id: const Uuid().v4(),
        name: farmName,
        ownerName: contactPerson ?? user.fullName,
        ownerContact: contactPhone ?? user.phoneNumber ?? '',
        ownerEmail: contactEmail ?? user.email ?? '',
        farmType: FarmType.mixed, // Default farm type
        cattleCount: 0, // Default cattle count
        capacity: 100, // Default capacity
        createdAt: now,
        updatedAt: now,
      );

      // Set location
      farm.location = location;

      // Save the farm using the repository
      await _farmSetupRepository.addFarm(farm);

      // Update user's farm information
      user.updateProfile(
        farmName: farmName,
        farmLocation: location,
        farmDescription: description,
      );

      await _userRepository.updateUser(user);

      _logger.info('Farm created for user: $user.email');
      return ProfileResult.success('Farm created successfully');

    } catch (e) {
      _logger.severe('Error creating farm: $e');
      return ProfileResult.failure('Failed to create farm');
    }
  }

  /// Get user's associated farms
  Future<List<FarmIsar>> getUserFarms() async {
    try {
      if (!_authService.isAuthenticated) {
        return [];
      }

      final user = _authService.currentUser!;

      // For now, get farms by matching owner email
      // In a full implementation, you'd have proper user-farm relationships
      final allFarms = await _farmSetupRepository.getAllFarms();
      return allFarms.where((farm) =>
        farm.ownerEmail?.toLowerCase() == user.email?.toLowerCase()
      ).toList();

    } catch (e) {
      _logger.severe('Error getting user farms: $e');
      return [];
    }
  }

  /// Get profile completion percentage
  int getProfileCompletionPercentage() {
    if (!_authService.isAuthenticated) return 0;

    final user = _authService.currentUser!;
    int completedFields = 0;
    const int totalFields = 8;

    if (user.firstName?.isNotEmpty == true) completedFields++;
    if (user.lastName?.isNotEmpty == true) completedFields++;
    if (user.email?.isNotEmpty == true) completedFields++;
    if (user.phoneNumber?.isNotEmpty == true) completedFields++;
    if (user.farmName?.isNotEmpty == true) completedFields++;
    if (user.farmLocation?.isNotEmpty == true) completedFields++;
    if (user.profilePictureUrl?.isNotEmpty == true) completedFields++;
    if (user.isEmailVerified) completedFields++;

    return ((completedFields / totalFields) * 100).round();
  }

  /// Get profile picture file
  File? getProfilePictureFile() {
    if (!_authService.isAuthenticated) return null;
    
    final user = _authService.currentUser!;
    if (user.profilePictureUrl?.isNotEmpty == true) {
      final file = File(user.profilePictureUrl!);
      if (file.existsSync()) {
        return file;
      }
    }
    return null;
  }

  /// Private helper methods

  /// Sync user farm information with farm records
  Future<void> _syncUserFarmInfo(UserIsar user) async {
    try {
      final userFarms = await getUserFarms();

      for (final farm in userFarms) {
        bool needsUpdate = false;

        if (user.farmName != null && farm.name != user.farmName) {
          farm.name = user.farmName!;
          needsUpdate = true;
        }

        if (user.farmLocation != null && farm.location != user.farmLocation) {
          farm.location = user.farmLocation!;
          needsUpdate = true;
        }

        // Note: FarmIsar doesn't have a description field, so we skip that

        if (needsUpdate) {
          farm.updatedAt = DateTime.now();
          await _farmSetupRepository.updateFarm(farm);
        }
      }
    } catch (e) {
      _logger.warning('Error syncing user farm info: $e');
    }
  }
}

/// Result class for profile operations
class ProfileResult {
  final bool success;
  final String message;

  ProfileResult._(this.success, this.message);

  factory ProfileResult.success(String message) => ProfileResult._(true, message);
  factory ProfileResult.failure(String message) => ProfileResult._(false, message);
}
