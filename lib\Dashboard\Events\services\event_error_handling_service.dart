import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../utils/message_utils.dart';

/// Comprehensive error handling service for Events module
/// Provides retry mechanisms, offline support, and user-friendly error messages
/// Following established error handling patterns from other modules
class EventErrorHandlingService {
  static final EventErrorHandlingService _instance = EventErrorHandlingService._internal();
  factory EventErrorHandlingService() => _instance;
  EventErrorHandlingService._internal();

  final Logger _logger = Logger('EventErrorHandlingService');
  final Map<String, int> _retryAttempts = {};
  final Map<String, DateTime> _lastRetryTime = {};
  final List<Map<String, dynamic>> _offlineQueue = [];

  // Maximum retry attempts for different operations
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  static const Duration maxRetryDelay = Duration(seconds: 30);

  // ===== ERROR MESSAGE HANDLING =====

  /// Handle event-specific errors and return user-friendly messages
  String handleEventError(dynamic error, {String? context}) {
    _logger.severe('Event error${context != null ? ' in $context' : ''}: $error');

    if (error is ValidationException) {
      return error.message;
    }

    if (error is DatabaseException) {
      return _handleDatabaseError(error);
    }

    if (error is RecordNotFoundException) {
      return _handleRecordNotFoundError(error, context);
    }

    // Handle specific error types
    final errorString = error.toString().toLowerCase();

    // Network and connectivity errors
    if (errorString.contains('network') || 
        errorString.contains('connection') || 
        errorString.contains('socket')) {
      return 'Network connection error. Please check your internet connection and try again.';
    }

    if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    }

    // File and storage errors
    if (errorString.contains('file not found') || 
        errorString.contains('path not found')) {
      return 'File not found. The file may have been moved or deleted.';
    }

    if (errorString.contains('permission denied') || 
        errorString.contains('access denied')) {
      return 'Permission denied. Please check file permissions and try again.';
    }

    if (errorString.contains('storage') || 
        errorString.contains('disk full')) {
      return 'Storage error. Please free up space and try again.';
    }

    // Event-specific errors
    if (errorString.contains('event not found')) {
      return 'Event not found. It may have been deleted by another user.';
    }

    if (errorString.contains('cattle not found')) {
      return 'Selected cattle not found. Please select a different cattle.';
    }

    if (errorString.contains('event type not found')) {
      return 'Selected event type not found. Please select a different event type.';
    }

    if (errorString.contains('duplicate') || 
        errorString.contains('already exists')) {
      return 'This event already exists. Please check your data and try again.';
    }

    // Attachment errors
    if (errorString.contains('file too large') || 
        errorString.contains('size limit')) {
      return 'File is too large. Maximum file size is 50MB.';
    }

    if (errorString.contains('invalid file format') || 
        errorString.contains('unsupported format')) {
      return 'Invalid file format. Please select a supported file type.';
    }

    if (errorString.contains('attachment limit')) {
      return 'Maximum number of attachments reached. You can have up to 10 attachments per event.';
    }

    // Recurrence errors
    if (errorString.contains('recurrence') || 
        errorString.contains('recurring')) {
      return 'Invalid recurrence settings. Please check your recurrence configuration.';
    }

    // Date and time errors
    if (errorString.contains('invalid date') || 
        errorString.contains('date format')) {
      return 'Invalid date format. Please select a valid date.';
    }

    if (errorString.contains('date range') || 
        errorString.contains('date conflict')) {
      return 'Invalid date range. Please check your date selection.';
    }

    // Notification errors
    if (errorString.contains('notification') || 
        errorString.contains('reminder')) {
      return 'Failed to set up notifications. Event saved but reminders may not work.';
    }

    // Automation errors
    if (errorString.contains('automation') || 
        errorString.contains('auto-generated')) {
      return 'Event automation failed. Please create the event manually.';
    }

    // Generic fallback
    return 'An unexpected error occurred. Please try again later.';
  }

  /// Handle database-specific errors
  String _handleDatabaseError(DatabaseException error) {
    final message = error.message.toLowerCase();

    if (message.contains('connection')) {
      return 'Database connection error. Please try again later.';
    }

    if (message.contains('timeout')) {
      return 'Database operation timed out. Please try again.';
    }

    if (message.contains('constraint') || message.contains('unique')) {
      return 'This data already exists. Please use different values.';
    }

    if (message.contains('foreign key') || message.contains('reference')) {
      return 'Cannot complete operation due to data dependencies. Please check related records.';
    }

    if (message.contains('lock') || message.contains('busy')) {
      return 'Database is busy. Please wait a moment and try again.';
    }

    if (message.contains('corrupt') || message.contains('integrity')) {
      return 'Database integrity error. Please contact support.';
    }

    return 'Database error occurred. Please try again later.';
  }

  /// Handle record not found errors
  String _handleRecordNotFoundError(RecordNotFoundException error, String? context) {
    switch (context?.toLowerCase()) {
      case 'event':
        return 'Event not found. It may have been deleted.';
      case 'cattle':
        return 'Selected cattle not found. Please select a different cattle.';
      case 'event type':
        return 'Selected event type not found. Please select a different event type.';
      case 'attachment':
        return 'Attachment not found. It may have been removed.';
      default:
        return error.message;
    }
  }

  // ===== RETRY MECHANISMS =====

  /// Execute operation with retry logic
  Future<T> executeWithRetry<T>(
    String operationId,
    Future<T> Function() operation, {
    int? maxAttempts,
    Duration? delay,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    final attempts = maxAttempts ?? maxRetryAttempts;
    final retryDelay = delay ?? EventErrorHandlingService.retryDelay;
    
    for (int attempt = 1; attempt <= attempts; attempt++) {
      try {
        final result = await operation();
        
        // Clear retry tracking on success
        _retryAttempts.remove(operationId);
        _lastRetryTime.remove(operationId);
        
        return result;
      } catch (error) {
        _logger.warning('Operation $operationId failed (attempt $attempt/$attempts): $error');
        
        // Check if we should retry
        final canRetry = shouldRetry?.call(error) ?? _isRetryableError(error);
        
        if (attempt < attempts && canRetry) {
          // Calculate delay with exponential backoff
          final currentDelay = Duration(
            milliseconds: (retryDelay.inMilliseconds * attempt).clamp(
              retryDelay.inMilliseconds,
              maxRetryDelay.inMilliseconds,
            ),
          );
          
          _logger.info('Retrying operation $operationId in ${currentDelay.inSeconds} seconds...');
          
          // Update retry tracking
          _retryAttempts[operationId] = attempt;
          _lastRetryTime[operationId] = DateTime.now();
          
          await Future.delayed(currentDelay);
        } else {
          // Max attempts reached or non-retryable error
          _retryAttempts.remove(operationId);
          _lastRetryTime.remove(operationId);
          
          _logger.severe('Operation $operationId failed after $attempt attempts: $error');
          rethrow;
        }
      }
    }
    
    throw Exception('Operation $operationId failed after $attempts attempts');
  }

  /// Check if error is retryable
  bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Network errors are usually retryable
    if (errorString.contains('network') || 
        errorString.contains('connection') || 
        errorString.contains('timeout') ||
        errorString.contains('socket')) {
      return true;
    }

    // Database busy/lock errors are retryable
    if (errorString.contains('busy') || 
        errorString.contains('lock') ||
        errorString.contains('deadlock')) {
      return true;
    }

    // Temporary file system errors
    if (errorString.contains('temporary') || 
        errorString.contains('try again')) {
      return true;
    }

    // Validation errors are not retryable
    if (error is ValidationException) {
      return false;
    }

    // Record not found errors are not retryable
    if (error is RecordNotFoundException) {
      return false;
    }

    // Default to not retryable for safety
    return false;
  }

  /// Get retry status for operation
  Map<String, dynamic> getRetryStatus(String operationId) {
    return {
      'attempts': _retryAttempts[operationId] ?? 0,
      'lastRetry': _lastRetryTime[operationId],
      'canRetry': (_retryAttempts[operationId] ?? 0) < maxRetryAttempts,
    };
  }

  // ===== OFFLINE SUPPORT =====

  /// Queue operation for offline execution
  void queueOfflineOperation(String operationType, Map<String, dynamic> data) {
    final operation = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'type': operationType,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
      'attempts': 0,
    };

    _offlineQueue.add(operation);
    _logger.info('Queued offline operation: $operationType');
  }

  /// Process offline queue when connection is restored
  Future<void> processOfflineQueue() async {
    if (_offlineQueue.isEmpty) return;

    _logger.info('Processing ${_offlineQueue.length} offline operations...');

    final failedOperations = <Map<String, dynamic>>[];

    for (final operation in List.from(_offlineQueue)) {
      try {
        await _processOfflineOperation(operation);
        _offlineQueue.remove(operation);
        _logger.info('Successfully processed offline operation: ${operation['type']}');
      } catch (error) {
        operation['attempts'] = (operation['attempts'] ?? 0) + 1;
        
        if (operation['attempts'] >= maxRetryAttempts) {
          _offlineQueue.remove(operation);
          failedOperations.add(operation);
          _logger.severe('Failed to process offline operation after ${operation['attempts']} attempts: ${operation['type']}');
        } else {
          _logger.warning('Offline operation failed (attempt ${operation['attempts']}): ${operation['type']}');
        }
      }
    }

    if (failedOperations.isNotEmpty) {
      _logger.warning('${failedOperations.length} offline operations failed permanently');
    }
  }

  /// Process individual offline operation
  Future<void> _processOfflineOperation(Map<String, dynamic> operation) async {
    final type = operation['type'] as String;

    switch (type) {
      case 'create_event':
        // Implementation would depend on your event creation logic
        throw UnimplementedError('Offline event creation not implemented');
      
      case 'update_event':
        // Implementation would depend on your event update logic
        throw UnimplementedError('Offline event update not implemented');
      
      case 'delete_event':
        // Implementation would depend on your event deletion logic
        throw UnimplementedError('Offline event deletion not implemented');
      
      case 'upload_attachment':
        // Implementation would depend on your attachment upload logic
        throw UnimplementedError('Offline attachment upload not implemented');
      
      default:
        throw Exception('Unknown offline operation type: $type');
    }
  }

  /// Check if device is online
  Future<bool> isOnline() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Get offline queue status
  Map<String, dynamic> getOfflineQueueStatus() {
    return {
      'queueSize': _offlineQueue.length,
      'operations': _offlineQueue.map((op) => {
        'id': op['id'],
        'type': op['type'],
        'timestamp': op['timestamp'],
        'attempts': op['attempts'],
      }).toList(),
    };
  }

  // ===== UI ERROR HANDLING =====

  /// Show error dialog with retry option
  Future<bool> showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    String? operationId,
    VoidCallback? onRetry,
    bool showRetry = true,
  }) async {
    final canRetry = operationId != null && 
                    showRetry && 
                    getRetryStatus(operationId)['canRetry'] == true;

    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[700]),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            if (operationId != null) ...[
              const SizedBox(height: 16),
              Text(
                'Operation ID: $operationId',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          if (canRetry && onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                onRetry();
              },
              child: const Text('Retry'),
            ),
          if (!canRetry)
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('OK'),
            ),
        ],
      ),
    ) ?? false;
  }

  /// Show error snackbar with retry option
  void showErrorSnackBar(
    BuildContext context, {
    required String message,
    String? operationId,
    VoidCallback? onRetry,
    Duration? duration,
  }) {
    final canRetry = operationId != null && 
                    getRetryStatus(operationId)['canRetry'] == true;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red[700],
        duration: duration ?? const Duration(seconds: 6),
        action: canRetry && onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : SnackBarAction(
                label: 'Dismiss',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
      ),
    );
  }

  /// Show success message after retry
  void showRetrySuccessMessage(BuildContext context, String operation) {
    MessageUtils.showSuccess(
      context,
      'Operation completed successfully after retry: $operation',
    );
  }

  /// Show offline queue status
  void showOfflineQueueStatus(BuildContext context) {
    final status = getOfflineQueueStatus();
    final queueSize = status['queueSize'] as int;

    if (queueSize == 0) {
      MessageUtils.showInfo(context, 'No pending offline operations');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Offline Operations'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('$queueSize operations pending'),
            const SizedBox(height: 16),
            const Text('Operations will be processed when connection is restored.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await processOfflineQueue();
                if (context.mounted) {
                  MessageUtils.showSuccess(context, 'Offline operations processed');
                }
              } catch (e) {
                if (context.mounted) {
                  MessageUtils.showError(context, 'Failed to process offline operations');
                }
              }
            },
            child: const Text('Process Now'),
          ),
        ],
      ),
    );
  }

  // ===== LOGGING AND DEBUGGING =====

  /// Log error for debugging
  void logError(String context, dynamic error, [StackTrace? stackTrace]) {
    _logger.severe('Error in $context: $error', error, stackTrace);
  }

  /// Log warning
  void logWarning(String context, String message) {
    _logger.warning('Warning in $context: $message');
  }

  /// Log info
  void logInfo(String context, String message) {
    _logger.info('Info in $context: $message');
  }

  /// Get error statistics
  Map<String, dynamic> getErrorStatistics() {
    return {
      'totalRetryAttempts': _retryAttempts.values.fold(0, (sum, attempts) => sum + attempts),
      'activeRetries': _retryAttempts.length,
      'offlineQueueSize': _offlineQueue.length,
      'lastErrors': _lastRetryTime.entries.map((entry) => {
        'operationId': entry.key,
        'lastRetry': entry.value.toIso8601String(),
        'attempts': _retryAttempts[entry.key] ?? 0,
      }).toList(),
    };
  }

  /// Clear error tracking
  void clearErrorTracking() {
    _retryAttempts.clear();
    _lastRetryTime.clear();
    _offlineQueue.clear();
    _logger.info('Error tracking cleared');
  }

  /// Dispose resources
  void dispose() {
    clearErrorTracking();
  }
}

/// Event-specific message utilities extending the base MessageUtils
class EventMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== EVENT-SPECIFIC MESSAGES =====

  /// Event CRUD messages
  static String eventCreated() => 'Event created successfully';
  static String eventUpdated() => 'Event updated successfully';
  static String eventDeleted() => 'Event deleted successfully';
  static String eventCompleted() => 'Event marked as completed';
  static String eventScheduled() => 'Event scheduled successfully';

  /// Event type messages
  static String eventTypeCreated() => 'Event type created successfully';
  static String eventTypeUpdated() => 'Event type updated successfully';
  static String eventTypeDeleted() => 'Event type deleted successfully';

  /// Attachment messages
  static String attachmentAdded() => 'Attachment added successfully';
  static String attachmentRemoved() => 'Attachment removed successfully';
  static String attachmentUploaded() => 'File uploaded successfully';

  /// Recurrence messages
  static String recurringEventsCreated(int count) => '$count recurring events created successfully';
  static String recurrenceUpdated() => 'Recurrence settings updated successfully';

  /// Notification messages
  static String notificationsEnabled() => 'Event notifications enabled';
  static String notificationsDisabled() => 'Event notifications disabled';
  static String reminderSet() => 'Reminder set successfully';

  /// Automation messages
  static String eventAutoCreated() => 'Event created automatically';
  static String automationEnabled() => 'Event automation enabled';
  static String automationDisabled() => 'Event automation disabled';

  // ===== EVENT-SPECIFIC VALIDATION MESSAGES =====

  static String get eventTitleRequired => 'Event title is required';
  static String get scheduledDateRequired => 'Scheduled date is required';
  static String get cattleRequired => 'Please select cattle for this event';
  static String get eventTypeRequired => 'Please select an event type';
  static String get invalidDateRange => 'Invalid date range selected';
  static String get fileTooLarge => 'File size cannot exceed 50MB';
  static String get tooManyAttachments => 'Cannot exceed 10 attachments per event';
  static String get invalidRecurrenceSettings => 'Invalid recurrence settings';

  // ===== EVENT DELETE CONFIRMATIONS =====

  /// Show event delete confirmation
  static Future<bool?> showEventDeleteConfirmation(
    BuildContext context, {
    String? eventTitle,
    String? eventId,
    int attachmentCount = 0,
    bool hasRecurringEvents = false,
    List<String> specificWarnings = const [],
  }) async {
    List<String> warnings = [];
    
    if (attachmentCount > 0) {
      warnings.add('$attachmentCount ${attachmentCount == 1 ? 'attachment' : 'attachments'}');
    }
    
    if (hasRecurringEvents) {
      warnings.add('All recurring instances of this event');
    }
    
    warnings.addAll(specificWarnings);

    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'event',
      itemName: eventTitle,
      recordId: eventId,
      relatedRecords: warnings,
    );
  }

  /// Show event type delete confirmation
  static Future<bool?> showEventTypeDeleteConfirmation(
    BuildContext context, {
    String? eventTypeName,
    String? eventTypeId,
    int eventCount = 0,
    List<String> specificWarnings = const [],
  }) async {
    List<String> warnings = [];
    
    if (eventCount > 0) {
      warnings.add('$eventCount ${eventCount == 1 ? 'event' : 'events'} using this type');
    }
    
    warnings.addAll(specificWarnings);

    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'event type',
      itemName: eventTypeName,
      recordId: eventTypeId,
      relatedRecords: warnings,
    );
  }

  /// Show attachment delete confirmation
  static Future<bool?> showAttachmentDeleteConfirmation(
    BuildContext context, {
    String? fileName,
    String? fileSize,
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'attachment',
      itemName: fileName,
      customWarning: fileSize != null ? 'File size: $fileSize' : null,
    );
  }

  // ===== EVENT-SPECIFIC ERROR HANDLING =====

  /// Show event error with context
  static void showEventError(
    BuildContext context,
    String operation,
    String details, {
    Duration? duration,
  }) {
    final errorService = EventErrorHandlingService();
    final friendlyMessage = errorService.handleEventError(
      Exception(details),
      context: operation,
    );
    
    showError(context, friendlyMessage, duration: duration);
  }

  /// Show validation error
  static void showValidationError(
    BuildContext context,
    String validationMessage, {
    Duration? duration,
  }) {
    showError(context, validationMessage, duration: duration);
  }

  /// Show network error with retry option
  static void showNetworkError(
    BuildContext context, {
    VoidCallback? onRetry,
    Duration? duration,
  }) {
    const message = 'Network connection error. Please check your internet connection.';
    
    if (onRetry != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.wifi_off, color: Colors.white),
              SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red[700],
          duration: duration ?? const Duration(seconds: 6),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: onRetry,
          ),
        ),
      );
    } else {
      showError(context, message, duration: duration);
    }
  }
}