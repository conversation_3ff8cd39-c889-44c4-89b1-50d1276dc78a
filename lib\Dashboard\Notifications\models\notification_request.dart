import 'notification_priority.dart';

/// Request model for creating notifications
class NotificationRequest {
  /// Title of the notification
  final String title;
  
  /// Main content/message of the notification
  final String message;
  
  /// Category of the notification (health, breeding, milk, weight, events, system)
  final String category;
  
  /// Type of notification (reminder, alert, info, warning, error)
  final String type;
  
  /// Priority level of the notification
  final NotificationPriority priority;
  
  /// Related cattle ID if applicable
  final String? cattleId;
  
  /// Related record ID if applicable
  final String? relatedRecordId;
  
  /// Type of the related record
  final String? relatedRecordType;
  
  /// Link to event system
  final String? eventId;
  
  /// URL to an image associated with the notification
  final String? imageUrl;
  
  /// Deep link for navigation within the app
  final String? actionUrl;
  
  /// Flexible metadata for additional information
  final Map<String, String>? customData;
  
  /// When the notification is scheduled to be shown
  final DateTime? scheduledFor;
  
  /// When the notification expires
  final DateTime? expiresAt;
  
  /// Whether the notification recurs
  final bool isRecurring;
  
  /// Pattern for recurring notifications (daily, weekly, monthly, custom)
  final String? recurringPattern;

  /// Constructor
  NotificationRequest({
    required this.title,
    required this.message,
    required this.category,
    required this.type,
    this.priority = NotificationPriority.medium,
    this.cattleId,
    this.relatedRecordId,
    this.relatedRecordType,
    this.eventId,
    this.imageUrl,
    this.actionUrl,
    this.customData,
    this.scheduledFor,
    this.expiresAt,
    this.isRecurring = false,
    this.recurringPattern,
  });

  /// Convert to a map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'message': message,
      'category': category,
      'type': type,
      'priority': priority.index,
      'cattleId': cattleId,
      'relatedRecordId': relatedRecordId,
      'relatedRecordType': relatedRecordType,
      'eventId': eventId,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'customData': customData,
      'scheduledFor': scheduledFor?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'isRecurring': isRecurring,
      'recurringPattern': recurringPattern,
    };
  }

  /// Create from a map (JSON deserialization)
  factory NotificationRequest.fromMap(Map<String, dynamic> map) {
    return NotificationRequest(
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      category: map['category'] ?? '',
      type: map['type'] ?? '',
      priority: map['priority'] != null 
          ? NotificationPriority.values[map['priority']] 
          : NotificationPriority.medium,
      cattleId: map['cattleId'],
      relatedRecordId: map['relatedRecordId'],
      relatedRecordType: map['relatedRecordType'],
      eventId: map['eventId'],
      imageUrl: map['imageUrl'],
      actionUrl: map['actionUrl'],
      customData: map['customData'] != null 
          ? Map<String, String>.from(map['customData']) 
          : null,
      scheduledFor: map['scheduledFor'] != null 
          ? DateTime.parse(map['scheduledFor']) 
          : null,
      expiresAt: map['expiresAt'] != null 
          ? DateTime.parse(map['expiresAt']) 
          : null,
      isRecurring: map['isRecurring'] ?? false,
      recurringPattern: map['recurringPattern'],
    );
  }
}