import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../services/events_analytics_service.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Performance optimization service for Events module
/// Implements caching, pagination, lazy loading, and background processing
class EventPerformanceService {
  static final Logger _logger = Logger('EventPerformanceService');
  
  // Cache configuration
  static const int _maxCacheSize = 1000;
  static const Duration _cacheExpiry = Duration(minutes: 5);
  static const int _defaultPageSize = 50;
  
  // Analytics cache
  static EventAnalyticsResult? _cachedAnalytics;
  static DateTime? _analyticsLastCalculated;
  static const Duration _analyticsCacheExpiry = Duration(minutes: 2);
  
  // Query result cache with LRU eviction
  static final LinkedHashMap<String, _CachedQuery> _queryCache = LinkedHashMap();
  
  // Pagination cache
  static final Map<String, _PaginationState> _paginationCache = {};
  
  // Background processing
  static Timer? _backgroundTimer;
  static bool _isBackgroundProcessingEnabled = true;
  
  /// Initialize performance service
  static void initialize() {
    _logger.info('Initializing Event Performance Service');
    _startBackgroundProcessing();
  }
  
  /// Dispose performance service
  static void dispose() {
    _logger.info('Disposing Event Performance Service');
    _stopBackgroundProcessing();
    clearAllCaches();
  }
  
  /// Get cached analytics or calculate if expired
  static EventAnalyticsResult getCachedAnalytics(
    List<EventIsar> events,
    List<EventTypeIsar> eventTypes,
    List<EventAttachmentIsar> attachments,
    List<CattleIsar> cattle,
  ) {
    final now = DateTime.now();
    
    // Check if cache is valid
    if (_cachedAnalytics != null && 
        _analyticsLastCalculated != null &&
        now.difference(_analyticsLastCalculated!).compareTo(_analyticsCacheExpiry) < 0) {
      _logger.fine('Returning cached analytics');
      return _cachedAnalytics!;
    }
    
    // Calculate new analytics
    _logger.fine('Calculating fresh analytics');
    final stopwatch = Stopwatch()..start();
    
    _cachedAnalytics = EventAnalyticsService.calculate(
      events,
      eventTypes,
      attachments,
      cattle,
    );
    _analyticsLastCalculated = now;
    
    stopwatch.stop();
    _logger.info('Analytics calculated in ${stopwatch.elapsedMilliseconds}ms');
    
    return _cachedAnalytics!;
  }
  
  /// Get paginated events with caching
  static Future<PaginatedResult<EventIsar>> getPaginatedEvents(
    List<EventIsar> allEvents, {
    int page = 0,
    int pageSize = _defaultPageSize,
    String? cacheKey,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    // Generate cache key if not provided
    cacheKey ??= 'paginated_${allEvents.length}_${page}_$pageSize';
    
    // Check cache first
    final cached = _getFromQueryCache(cacheKey);
    if (cached != null && cached.data is PaginatedResult<EventIsar>) {
      _logger.fine('Returning cached paginated result for page $page');
      return cached.data as PaginatedResult<EventIsar>;
    }
    
    // Calculate pagination
    final totalItems = allEvents.length;
    final totalPages = (totalItems / pageSize).ceil();
    final startIndex = page * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, totalItems);
    
    final pageItems = allEvents.sublist(startIndex, endIndex);
    
    final result = PaginatedResult<EventIsar>(
      items: pageItems,
      currentPage: page,
      totalPages: totalPages,
      totalItems: totalItems,
      pageSize: pageSize,
      hasNextPage: page < totalPages - 1,
      hasPreviousPage: page > 0,
    );
    
    // Cache the result
    _addToQueryCache(cacheKey, result);
    
    stopwatch.stop();
    _logger.fine('Paginated events calculated in ${stopwatch.elapsedMilliseconds}ms');
    
    return result;
  }
  
  /// Get lazy-loaded event attachments
  static Future<List<EventAttachmentIsar>> getLazyAttachments(
    String eventBusinessId,
    List<EventAttachmentIsar> allAttachments,
  ) async {
    final cacheKey = 'attachments_$eventBusinessId';
    
    // Check cache first
    final cached = _getFromQueryCache(cacheKey);
    if (cached != null && cached.data is List<EventAttachmentIsar>) {
      return cached.data as List<EventAttachmentIsar>;
    }
    
    // Filter attachments for this event
    final eventAttachments = allAttachments
        .where((attachment) => attachment.eventBusinessId == eventBusinessId)
        .toList();
    
    // Cache the result
    _addToQueryCache(cacheKey, eventAttachments);
    
    return eventAttachments;
  }
  
  /// Optimize calendar rendering for large datasets
  static Map<DateTime, List<EventIsar>> getOptimizedCalendarEvents(
    List<EventIsar> allEvents,
    DateTime startDate,
    DateTime endDate,
  ) {
    final cacheKey = 'calendar_${startDate.millisecondsSinceEpoch}_${endDate.millisecondsSinceEpoch}';
    
    // Check cache first
    final cached = _getFromQueryCache(cacheKey);
    if (cached != null && cached.data is Map<DateTime, List<EventIsar>>) {
      return cached.data as Map<DateTime, List<EventIsar>>;
    }
    
    final stopwatch = Stopwatch()..start();
    final calendarEvents = <DateTime, List<EventIsar>>{};
    
    // Filter events within date range
    final filteredEvents = allEvents.where((event) {
      if (event.scheduledDate == null) return false;
      final eventDate = event.scheduledDate!;
      return eventDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
             eventDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
    
    // Group events by date
    for (final event in filteredEvents) {
      if (event.scheduledDate == null) continue;
      
      final dateKey = DateTime(
        event.scheduledDate!.year,
        event.scheduledDate!.month,
        event.scheduledDate!.day,
      );
      
      calendarEvents.putIfAbsent(dateKey, () => []).add(event);
    }
    
    // Sort events within each day
    for (final dayEvents in calendarEvents.values) {
      dayEvents.sort((a, b) {
        if (a.scheduledDate == null && b.scheduledDate == null) return 0;
        if (a.scheduledDate == null) return 1;
        if (b.scheduledDate == null) return -1;
        return a.scheduledDate!.compareTo(b.scheduledDate!);
      });
    }
    
    // Cache the result
    _addToQueryCache(cacheKey, calendarEvents);
    
    stopwatch.stop();
    _logger.fine('Calendar events optimized in ${stopwatch.elapsedMilliseconds}ms');
    
    return calendarEvents;
  }
  
  /// Efficient stream management to prevent memory leaks
  static StreamSubscription<T> createManagedStream<T>(
    Stream<T> stream,
    void Function(T) onData, {
    Function? onError,
    void Function()? onDone,
    String? debugName,
  }) {
    _logger.fine('Creating managed stream: ${debugName ?? 'unnamed'}');
    
    return stream.listen(
      onData,
      onError: (error, stackTrace) {
        _logger.warning('Stream error in ${debugName ?? 'unnamed'}: $error');
        onError?.call(error);
      },
      onDone: () {
        _logger.fine('Stream completed: ${debugName ?? 'unnamed'}');
        onDone?.call();
      },
      cancelOnError: false,
    );
  }
  
  /// Background processing for automation and notifications
  static void _startBackgroundProcessing() {
    if (!_isBackgroundProcessingEnabled) return;
    
    _backgroundTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _performBackgroundTasks();
    });
    
    _logger.info('Background processing started');
  }
  
  static void _stopBackgroundProcessing() {
    _backgroundTimer?.cancel();
    _backgroundTimer = null;
    _logger.info('Background processing stopped');
  }
  
  static void _performBackgroundTasks() {
    try {
      // Clean expired cache entries
      _cleanExpiredCache();
      
      // Log cache statistics
      if (kDebugMode) {
        _logCacheStatistics();
      }
    } catch (e, stackTrace) {
      _logger.warning('Background task error: $e', e, stackTrace);
    }
  }
  
  /// Cache management methods
  static void _addToQueryCache(String key, dynamic data) {
    // Remove oldest entries if cache is full
    while (_queryCache.length >= _maxCacheSize) {
      final oldestKey = _queryCache.keys.first;
      _queryCache.remove(oldestKey);
    }
    
    _queryCache[key] = _CachedQuery(
      data: data,
      timestamp: DateTime.now(),
    );
  }
  
  static _CachedQuery? _getFromQueryCache(String key) {
    final cached = _queryCache[key];
    if (cached == null) return null;
    
    // Check if expired
    final now = DateTime.now();
    if (now.difference(cached.timestamp).compareTo(_cacheExpiry) > 0) {
      _queryCache.remove(key);
      return null;
    }
    
    // Move to end (LRU)
    _queryCache.remove(key);
    _queryCache[key] = cached;
    
    return cached;
  }
  
  static void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _queryCache.entries) {
      if (now.difference(entry.value.timestamp).compareTo(_cacheExpiry) > 0) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _queryCache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      _logger.fine('Cleaned ${expiredKeys.length} expired cache entries');
    }
  }
  
  static void _logCacheStatistics() {
    _logger.fine('Cache Statistics:');
    _logger.fine('  Query cache size: ${_queryCache.length}/$_maxCacheSize');
    _logger.fine('  Analytics cached: ${_cachedAnalytics != null}');
    _logger.fine('  Pagination states: ${_paginationCache.length}');
  }
  
  /// Clear all caches
  static void clearAllCaches() {
    _queryCache.clear();
    _paginationCache.clear();
    _cachedAnalytics = null;
    _analyticsLastCalculated = null;
    _logger.info('All caches cleared');
  }
  
  /// Clear specific cache
  static void clearCache(String key) {
    _queryCache.remove(key);
  }
  
  /// Clear analytics cache
  static void clearAnalyticsCache() {
    _cachedAnalytics = null;
    _analyticsLastCalculated = null;
    _logger.fine('Analytics cache cleared');
  }
  
  /// Get cache statistics
  static Map<String, dynamic> getCacheStatistics() {
    return {
      'queryCacheSize': _queryCache.length,
      'maxCacheSize': _maxCacheSize,
      'analyticsCached': _cachedAnalytics != null,
      'paginationStates': _paginationCache.length,
      'backgroundProcessingEnabled': _isBackgroundProcessingEnabled,
    };
  }
  
  /// Enable/disable background processing
  static void setBackgroundProcessing(bool enabled) {
    _isBackgroundProcessingEnabled = enabled;
    if (enabled) {
      _startBackgroundProcessing();
    } else {
      _stopBackgroundProcessing();
    }
  }
}

/// Cached query result
class _CachedQuery {
  final dynamic data;
  final DateTime timestamp;
  
  _CachedQuery({
    required this.data,
    required this.timestamp,
  });
}

/// Pagination state
class _PaginationState {
  final int currentPage;
  final int pageSize;
  final int totalItems;
  
  _PaginationState({
    required this.currentPage,
    required this.pageSize,
    required this.totalItems,
  });
}

/// Paginated result wrapper
class PaginatedResult<T> {
  final List<T> items;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int pageSize;
  final bool hasNextPage;
  final bool hasPreviousPage;
  
  const PaginatedResult({
    required this.items,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.pageSize,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });
  
  @override
  String toString() {
    return 'PaginatedResult(page: $currentPage/$totalPages, items: ${items.length}/$totalItems)';
  }
}