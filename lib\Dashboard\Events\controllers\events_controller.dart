import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:logging/logging.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../models/event_enums.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/events_repository.dart';
import '../services/events_analytics_service.dart';
import '../services/event_validation_service.dart';
import '../services/event_error_handling_service.dart';
import '../services/event_performance_service.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum
import '../../Cattle/services/cattle_repository.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class EventFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final EventCategory? category;
  final EventStatus? status;
  final EventPriority? priority;
  final String? cattleTagId;
  final String? eventTypeId;
  final String? eventType; // For backward compatibility with filters widget
  final bool? isAutoGenerated;
  final bool? isRecurring;
  final String? location;
  final String? completedBy;

  const EventFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.category,
    this.status,
    this.priority,
    this.cattleTagId,
    this.eventTypeId,
    this.eventType,
    this.isAutoGenerated,
    this.isRecurring,
    this.location,
    this.completedBy,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      category != null ||
      status != null ||
      priority != null ||
      (cattleTagId?.isNotEmpty ?? false) ||
      (eventTypeId?.isNotEmpty ?? false) ||
      (eventType?.isNotEmpty ?? false) ||
      isAutoGenerated != null ||
      isRecurring != null ||
      (location?.isNotEmpty ?? false) ||
      (completedBy?.isNotEmpty ?? false);

  /// Create a copy with updated values
  EventFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    EventCategory? category,
    EventStatus? status,
    EventPriority? priority,
    String? cattleTagId,
    String? eventTypeId,
    String? eventType,
    bool? isAutoGenerated,
    bool? isRecurring,
    String? location,
    String? completedBy,
    bool clearStartDate = false,
    bool clearEndDate = false,
  }) {
    return EventFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      category: category ?? this.category,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      cattleTagId: cattleTagId ?? this.cattleTagId,
      eventTypeId: eventTypeId ?? this.eventTypeId,
      eventType: eventType ?? this.eventType,
      isAutoGenerated: isAutoGenerated ?? this.isAutoGenerated,
      isRecurring: isRecurring ?? this.isRecurring,
      location: location ?? this.location,
      completedBy: completedBy ?? this.completedBy,
    );
  }

  /// Clear all filters
  static const EventFilterState empty = EventFilterState();

  /// Convert to string for debugging
  @override
  String toString() {
    return 'EventFilterState('
        'searchQuery: $searchQuery, '
        'startDate: $startDate, '
        'endDate: $endDate, '
        'category: $category, '
        'status: $status, '
        'priority: $priority, '
        'cattleTagId: $cattleTagId, '
        'eventTypeId: $eventTypeId, '
        'eventType: $eventType, '
        'isAutoGenerated: $isAutoGenerated, '
        'isRecurring: $isRecurring, '
        'location: $location, '
        'completedBy: $completedBy'
        ')';
  }

  /// Check equality for testing
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EventFilterState &&
        other.searchQuery == searchQuery &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.category == category &&
        other.status == status &&
        other.priority == priority &&
        other.cattleTagId == cattleTagId &&
        other.eventTypeId == eventTypeId &&
        other.eventType == eventType &&
        other.isAutoGenerated == isAutoGenerated &&
        other.isRecurring == isRecurring &&
        other.location == location &&
        other.completedBy == completedBy;
  }

  @override
  int get hashCode {
    return Object.hash(
      searchQuery,
      startDate,
      endDate,
      category,
      status,
      priority,
      cattleTagId,
      eventTypeId,
      eventType,
      isAutoGenerated,
      isRecurring,
      location,
      completedBy,
    );
  }
}

/// Reactive controller for the main events screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class EventsController extends ChangeNotifier {
  // Repositories and services
  final EventsRepository _eventsRepository = GetIt.instance<EventsRepository>();
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  final Isar _isar = GetIt.instance<Isar>();
  
  // Validation and error handling services
  late final EventValidationService _validationService;
  final EventErrorHandlingService _errorHandlingService = EventErrorHandlingService();
  final Logger _logger = Logger('EventsController');

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Following cattle module pattern
  StreamSubscription<List<EventIsar>>? _eventsStreamSubscription;
  StreamSubscription<List<EventTypeIsar>>? _eventTypesStreamSubscription;
  StreamSubscription<List<EventAttachmentIsar>>? _attachmentsStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<EventIsar> _unfilteredEvents = []; // Complete dataset for analytics calculations
  List<EventTypeIsar> _unfilteredEventTypes = []; // Complete event types dataset
  List<EventAttachmentIsar> _unfilteredAttachments = []; // Complete attachments dataset
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics

  List<EventIsar> _filteredEvents = []; // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  EventAnalyticsResult _analyticsResult = EventAnalyticsResult.empty;

  // Filter state management - decoupled from UI
  EventFilterState _currentFilters = EventFilterState.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered events for UI display
  /// This is what the EventListTab should show
  List<EventIsar> get events => List.unmodifiable(_filteredEvents);

  /// Returns the complete unfiltered events for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<EventIsar> get unfilteredEvents => List.unmodifiable(_unfilteredEvents);

  /// Returns the complete unfiltered event types for analytics
  List<EventTypeIsar> get unfilteredEventTypes => List.unmodifiable(_unfilteredEventTypes);

  /// Returns the complete unfiltered attachments for analytics
  List<EventAttachmentIsar> get unfilteredAttachments => List.unmodifiable(_unfilteredAttachments);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  EventAnalyticsResult get analytics => _analyticsResult;

  // Convenience getters for backward compatibility with UI
  int get totalEvents => _analyticsResult.totalEvents;
  int get completedEvents => _analyticsResult.completedEvents;
  int get scheduledEvents => _analyticsResult.scheduledEvents;
  int get overdueEvents => _analyticsResult.overdueEvents;
  int get upcomingEvents => _analyticsResult.upcomingEvents;
  double get completionRate => _analyticsResult.completionRate;

  // Additional getters for analytics tabs
  Map<String, int> get eventsByCategory => _analyticsResult.eventsByCategory;
  Map<String, int> get eventsByStatus => _analyticsResult.eventsByStatus;
  Map<String, int> get eventsByPriority => _analyticsResult.eventsByPriority;
  int get eventsThisWeek => _analyticsResult.eventsThisWeek;
  int get eventsThisMonth => _analyticsResult.eventsThisMonth;
  int get eventsNextWeek => _analyticsResult.eventsNextWeek;
  int get eventsNextMonth => _analyticsResult.eventsNextMonth;
  double get averageCompletionTime => _analyticsResult.averageCompletionTime;
  int get totalEventTypes => _analyticsResult.totalEventTypes;
  int get activeEventTypes => _analyticsResult.activeEventTypes;
  double get totalEstimatedCosts => _analyticsResult.totalEstimatedCosts;
  double get totalActualCosts => _analyticsResult.totalActualCosts;
  double get averageEventCost => _analyticsResult.averageEventCost;
  int get autoGeneratedEvents => _analyticsResult.autoGeneratedEvents;
  int get manualEvents => _analyticsResult.manualEvents;
  double get automationRate => _analyticsResult.automationRate;
  Map<String, int> get eventsBySourceModule => _analyticsResult.eventsBySourceModule;
  int get totalAttachments => _analyticsResult.totalAttachments;
  int get eventsWithAttachments => _analyticsResult.eventsWithAttachments;
  double get averageAttachmentsPerEvent => _analyticsResult.averageAttachmentsPerEvent;
  Map<String, int> get eventsByCattle => _analyticsResult.eventsByCattle;
  int get cattleWithEvents => _analyticsResult.cattleWithEvents;
  double get averageEventsPerCattle => _analyticsResult.averageEventsPerCattle;
  int get recurringEvents => _analyticsResult.recurringEvents;
  int get oneTimeEvents => _analyticsResult.oneTimeEvents;
  Map<String, int> get eventsByRecurrencePattern => _analyticsResult.eventsByRecurrencePattern;

  // Filter state access
  EventFilterState get currentFilters => _currentFilters;

  // Error handling access
  EventErrorHandlingService get errorHandlingService => _errorHandlingService;

  // Pagination support
  Future<PaginatedResult<EventIsar>> getPaginatedEvents({
    int page = 0,
    int pageSize = 50,
  }) async {
    return await EventPerformanceService.getPaginatedEvents(
      _filteredEvents,
      page: page,
      pageSize: pageSize,
      cacheKey: 'filtered_events_${_currentFilters.hashCode}',
    );
  }

  // Calendar optimization
  Map<DateTime, List<EventIsar>> getOptimizedCalendarEvents(
    DateTime startDate,
    DateTime endDate,
  ) {
    return EventPerformanceService.getOptimizedCalendarEvents(
      _filteredEvents,
      startDate,
      endDate,
    );
  }

  // Lazy attachment loading
  Future<List<EventAttachmentIsar>> getLazyAttachments(String eventBusinessId) async {
    return await EventPerformanceService.getLazyAttachments(
      eventBusinessId,
      _unfilteredAttachments,
    );
  }

  // Constructor
  EventsController() {
    _validationService = EventValidationService(_eventsRepository, _cattleRepository);
    _initializeStreamListeners();
  }

  @override
  void dispose() {
    // Cancel all stream subscriptions to prevent memory leaks
    _eventsStreamSubscription?.cancel();
    _eventTypesStreamSubscription?.cancel();
    _attachmentsStreamSubscription?.cancel();
    _cattleStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    
    // Clear performance caches
    EventPerformanceService.clearAllCaches();
    
    super.dispose();
  }

  /// Initialize stream listeners for real-time updates following cattle module pattern
  /// Using separate streams instead of StreamZip for better reliability with performance optimization
  void _initializeStreamListeners() {
    debugPrint('🔧 EVENTS CONTROLLER: Initializing stream listeners (cattle module pattern)...');

    // Events stream - primary data source with performance optimization
    _eventsStreamSubscription = EventPerformanceService.createManagedStream(
      _isar.eventIsars.where().watch(fireImmediately: true),
      (events) {
        debugPrint('🔄 EVENTS STREAM: Received ${events.length} events');
        if (events.isNotEmpty) {
          debugPrint('   First event ID: ${events.first.businessId}');
        }
        _unfilteredEvents = events;
        _updateAnalytics(); // Update analytics immediately when events change
        _updateFilteredDataAndNotify();
      },
      onError: (error) {
        debugPrint('❌ Events stream error: $error');
        _setState(ControllerState.error);
        _errorMessage = 'Failed to load events: $error';
      },
      debugName: 'EventsStream',
    );

    // Event types stream with performance optimization
    _eventTypesStreamSubscription = EventPerformanceService.createManagedStream(
      _isar.eventTypeIsars.where().watch(fireImmediately: true),
      (eventTypes) {
        debugPrint('🔄 EVENT TYPES STREAM: Received ${eventTypes.length} event types');
        _unfilteredEventTypes = eventTypes;
        _updateAnalytics();
      },
      onError: (error) {
        debugPrint('❌ Event types stream error: $error');
      },
      debugName: 'EventTypesStream',
    );

    // Attachments stream with performance optimization
    _attachmentsStreamSubscription = EventPerformanceService.createManagedStream(
      _isar.eventAttachmentIsars.where().watch(fireImmediately: true),
      (attachments) {
        debugPrint('🔄 ATTACHMENTS STREAM: Received ${attachments.length} attachments');
        _unfilteredAttachments = attachments;
        _updateAnalytics();
      },
      onError: (error) {
        debugPrint('❌ Attachments stream error: $error');
      },
      debugName: 'AttachmentsStream',
    );

    // Cattle stream with performance optimization
    _cattleStreamSubscription = EventPerformanceService.createManagedStream(
      _isar.cattleIsars.where().watch(fireImmediately: true),
      (cattle) {
        debugPrint('🔄 CATTLE STREAM: Received ${cattle.length} cattle records');
        _unfilteredCattle = cattle;
        _updateAnalytics();
      },
      onError: (error) {
        debugPrint('❌ Cattle stream error: $error');
      },
      debugName: 'CattleStream',
    );

    // Initially, filtered data equals unfiltered data (no filters applied)
    _hasActiveFilters = false;

    debugPrint('✅ EVENTS CONTROLLER: Stream listeners initialized');
  }

  /// Set controller state and notify listeners
  void _setState(ControllerState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// Calculate analytics using the dedicated service with caching
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = EventPerformanceService.getCachedAnalytics(
      _unfilteredEvents, // Use unfiltered data for accurate analytics
      _unfilteredEventTypes,
      _unfilteredAttachments,
      _unfilteredCattle,
    );
  }

  /// Update filtered data and notify listeners - following cattle module pattern
  void _updateFilteredDataAndNotify() {
    try {
      debugPrint('📊 EVENTS CONTROLLER: Updating filtered data and UI');

      // Update filtered data if no filters are active
      if (!_hasActiveFilters) {
        _filteredEvents = List.from(_unfilteredEvents);
        _setState(ControllerState.loaded);
      }

      debugPrint('🔔 EVENTS CONTROLLER: Notifying UI listeners');
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating filtered data: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update event data: $e';
      notifyListeners();
    }
  }

  /// Update analytics calculations
  void _updateAnalytics() async {
    try {
      _calculateAnalytics();
    } catch (e) {
      debugPrint('Error updating analytics: $e');
    }
  }

  /// Refresh all data - compatible with analytics tab refresh functionality
  Future<void> refresh() async {
    try {
      // Test: Manually query the database to see if records exist
      debugPrint('🔍 EVENTS CONTROLLER: Testing direct database query...');
      final directQuery = await _isar.eventIsars.where().findAll();
      debugPrint('📊 EVENTS CONTROLLER: Direct query found ${directQuery.length} records');
      if (directQuery.isNotEmpty) {
        debugPrint('   First record: ${directQuery.first.businessId}');
      }

      // Force analytics recalculation on unfiltered data
      if (_unfilteredEvents.isNotEmpty || _unfilteredEventTypes.isNotEmpty) {
        _calculateAnalytics();
      }

      // Notify listeners of the refresh
      debugPrint('🔔 Main events controller: Notifying UI listeners (refresh complete)');
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing event data: $e\n$stackTrace');
      throw Exception('Failed to refresh event data: ${e.toString()}');
    }
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(EventFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredEvents = List.from(_unfilteredEvents);
      debugPrint('🔔 Main events controller: Notifying UI listeners (no filters applied)');
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  /// Supports all filter types including full-text search and advanced filtering
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    EventCategory? category,
    EventStatus? status,
    EventPriority? priority,
    String? cattleTagId,
    String? eventTypeId,
    String? eventType,
    bool? isAutoGenerated,
    bool? isRecurring,
    String? location,
    String? completedBy,
    bool clearStartDate = false,
    bool clearEndDate = false,
  }) {
    debugPrint('🔧 EVENTS CONTROLLER: Updating filters with new values');
    
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      category: category,
      status: status,
      priority: priority,
      cattleTagId: cattleTagId,
      eventTypeId: eventTypeId,
      eventType: eventType,
      isAutoGenerated: isAutoGenerated,
      isRecurring: isRecurring,
      location: location,
      completedBy: completedBy,
      clearStartDate: clearStartDate,
      clearEndDate: clearEndDate,
    );
    
    applyFilters(newFilters);
  }

  /// Apply search filter only (convenience method for search bars)
  void applySearchFilter(String searchQuery) {
    debugPrint('🔍 EVENTS CONTROLLER: Applying search filter: "$searchQuery"');
    updateFilters(searchQuery: searchQuery.trim());
  }

  /// Apply date range filter (convenience method for date pickers)
  void applyDateRangeFilter(DateTime? startDate, DateTime? endDate) {
    debugPrint('🔍 EVENTS CONTROLLER: Applying date range filter: $startDate to $endDate');
    updateFilters(startDate: startDate, endDate: endDate);
  }

  /// Apply category filter (convenience method for category selection)
  void applyCategoryFilter(EventCategory? category) {
    debugPrint('🔍 EVENTS CONTROLLER: Applying category filter: $category');
    updateFilters(category: category);
  }

  /// Apply status filter (convenience method for status selection)
  void applyStatusFilter(EventStatus? status) {
    debugPrint('🔍 EVENTS CONTROLLER: Applying status filter: $status');
    updateFilters(status: status);
  }

  /// Apply cattle filter (convenience method for cattle selection)
  void applyCattleFilter(String? cattleTagId) {
    debugPrint('🔍 EVENTS CONTROLLER: Applying cattle filter: $cattleTagId');
    updateFilters(cattleTagId: cattleTagId);
  }

  /// Apply multiple filters at once (convenience method for complex filtering)
  void applyMultipleFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    EventCategory? category,
    EventStatus? status,
    EventPriority? priority,
    String? cattleTagId,
    String? eventTypeId,
    bool? isAutoGenerated,
    bool? isRecurring,
  }) {
    debugPrint('🔍 EVENTS CONTROLLER: Applying multiple filters');
    updateFilters(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      category: category,
      status: status,
      priority: priority,
      cattleTagId: cattleTagId,
      eventTypeId: eventTypeId,
      isAutoGenerated: isAutoGenerated,
      isRecurring: isRecurring,
    );
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(EventFilterState.empty);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<EventIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredEvents = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      debugPrint('🔔 Main events controller: Notifying UI listeners (filtered data update)');
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  /// Implements full-text search across event descriptions, notes, and cattle information
  dynamic _buildFilteredQuery(EventFilterState filterState) {
    debugPrint('🔍 EVENTS CONTROLLER: Building filtered query with filters: $filterState');
    
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.eventIsars.where();

    // Apply full-text search filter at database level
    // Searches across event descriptions, notes, and cattle information
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase().trim();
      debugPrint('🔍 EVENTS CONTROLLER: Applying search filter: "$searchTerm"');
      
      // Build comprehensive search across multiple fields
      currentQuery = currentQuery.filter().group((q) => q
          // Search in event title
          .titleContains(searchTerm, caseSensitive: false)
          .or()
          // Search in event description
          .descriptionContains(searchTerm, caseSensitive: false)
          .or()
          // Search in event notes
          .notesContains(searchTerm, caseSensitive: false)
          .or()
          // Search in cattle tag ID (for cattle-specific searches)
          .cattleTagIdContains(searchTerm, caseSensitive: false)
          .or()
          // Search in location
          .locationContains(searchTerm, caseSensitive: false)
          .or()
          // Search in completion notes
          .completionNotesContains(searchTerm, caseSensitive: false)
          .or()
          // Search in completed by field
          .completedByContains(searchTerm, caseSensitive: false)
          .or()
          // Search in source module for auto-generated events
          .sourceModuleContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level with proper indexing
    if (filterState.startDate != null) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying start date filter: ${filterState.startDate}');
      // Use indexed scheduledDate field for optimal performance
      currentQuery = currentQuery.filter().scheduledDateGreaterThan(filterState.startDate!);
    }
    
    if (filterState.endDate != null) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying end date filter: ${filterState.endDate}');
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().scheduledDateLessThan(inclusiveEndDate);
    }

    // Apply category filter using indexed field
    if (filterState.category != null) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying category filter: ${filterState.category}');
      currentQuery = currentQuery.filter().categoryEqualTo(filterState.category!);
    }

    // Apply status filter using indexed field
    if (filterState.status != null) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying status filter: ${filterState.status}');
      currentQuery = currentQuery.filter().statusEqualTo(filterState.status!);
    }

    // Apply priority filter
    if (filterState.priority != null) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying priority filter: ${filterState.priority}');
      currentQuery = currentQuery.filter().priorityEqualTo(filterState.priority!);
    }

    // Apply cattle filter using indexed cattleTagId field
    if (filterState.cattleTagId?.isNotEmpty ?? false) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying cattle filter: ${filterState.cattleTagId}');
      currentQuery = currentQuery.filter().cattleTagIdEqualTo(filterState.cattleTagId!);
    }

    // Apply event type filter using indexed eventTypeId field
    if (filterState.eventTypeId?.isNotEmpty ?? false) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying event type ID filter: ${filterState.eventTypeId}');
      currentQuery = currentQuery.filter().eventTypeIdEqualTo(filterState.eventTypeId!);
    }

    // Apply backward compatibility event type filter
    if (filterState.eventType?.isNotEmpty ?? false) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying event type filter: ${filterState.eventType}');
      currentQuery = currentQuery.filter().eventTypeIdEqualTo(filterState.eventType!);
    }

    // Apply automation filter
    if (filterState.isAutoGenerated != null) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying automation filter: ${filterState.isAutoGenerated}');
      currentQuery = currentQuery.filter().isAutoGeneratedEqualTo(filterState.isAutoGenerated!);
    }

    // Apply recurrence filter
    if (filterState.isRecurring != null) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying recurrence filter: ${filterState.isRecurring}');
      currentQuery = currentQuery.filter().isRecurringEqualTo(filterState.isRecurring!);
    }

    // Apply location filter
    if (filterState.location?.isNotEmpty ?? false) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying location filter: ${filterState.location}');
      currentQuery = currentQuery.filter().locationContains(filterState.location!, caseSensitive: false);
    }

    // Apply completed by filter
    if (filterState.completedBy?.isNotEmpty ?? false) {
      debugPrint('🔍 EVENTS CONTROLLER: Applying completed by filter: ${filterState.completedBy}');
      currentQuery = currentQuery.filter().completedByContains(filterState.completedBy!, caseSensitive: false);
    }

    // Apply sorting at database level for optimal performance
    // Use indexed scheduledDate field for efficient sorting
    currentQuery = currentQuery.sortByScheduledDateDesc(); // Default sort by date (newest first)

    debugPrint('✅ EVENTS CONTROLLER: Filtered query built successfully');
    return currentQuery;
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new event with comprehensive validation and error handling
  Future<void> addEvent(EventIsar event) async {
    final operationId = 'add_event_${event.businessId}';
    
    try {
      debugPrint('🎯 EVENTS CONTROLLER: Adding event');
      debugPrint('   Event ID: ${event.businessId}');
      debugPrint('   Title: ${event.title}');
      debugPrint('   Cattle ID: ${event.cattleTagId}');

      // Validate event data
      await _validationService.validateEvent(event);
      
      // Validate recurrence if applicable
      if (event.isRecurring == true) {
        await _validationService.validateRecurrence(event);
      }

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () => _eventsRepository.saveEvent(event),
      );

      debugPrint('✅ EVENTS CONTROLLER: Event saved to repository');
      debugPrint('   Stream will handle UI update automatically...');
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to add event: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'adding event');
      rethrow;
    }
  }

  /// Add new event type with validation and error handling
  Future<void> addEventType(EventTypeIsar eventType) async {
    final operationId = 'add_event_type_${eventType.businessId}';
    
    try {
      // Validate event type data
      await _validationService.validateEventType(eventType);

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () => _eventsRepository.saveEventType(eventType),
      );
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to add event type: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'adding event type');
      rethrow;
    }
  }

  /// Add new event attachment with validation and error handling
  Future<void> addEventAttachment(EventAttachmentIsar attachment) async {
    final operationId = 'add_attachment_${attachment.businessId}';
    
    try {
      // Validate attachment data
      await _validationService.validateEventAttachment(attachment);

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () => _eventsRepository.saveEventAttachment(attachment),
      );
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to add event attachment: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'adding attachment');
      rethrow;
    }
  }

  /// Update event with validation and error handling
  Future<void> updateEvent(EventIsar updatedEvent) async {
    final operationId = 'update_event_${updatedEvent.businessId}';
    
    try {
      // Validate event data for update
      await _validationService.validateEvent(updatedEvent, isUpdate: true);
      
      // Validate recurrence if applicable
      if (updatedEvent.isRecurring == true) {
        await _validationService.validateRecurrence(updatedEvent);
      }

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () => _eventsRepository.saveEvent(updatedEvent),
      );
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to update event: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'updating event');
      rethrow;
    }
  }

  /// Update event type with validation and error handling
  Future<void> updateEventType(EventTypeIsar updatedEventType) async {
    final operationId = 'update_event_type_${updatedEventType.businessId}';
    
    try {
      // Validate event type data for update
      await _validationService.validateEventType(updatedEventType, isUpdate: true);

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () => _eventsRepository.saveEventType(updatedEventType),
      );
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to update event type: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'updating event type');
      rethrow;
    }
  }

  /// Complete event with validation and error handling
  Future<void> completeEvent(String businessId, {
    String? completedBy,
    String? completionNotes,
    double? actualCost,
  }) async {
    final operationId = 'complete_event_$businessId';
    
    try {
      debugPrint('🎯 EVENTS CONTROLLER: Completing event with business ID: $businessId');
      
      // Find the event by businessId first
      final event = _unfilteredEvents.firstWhere(
        (e) => e.businessId == businessId,
        orElse: () => throw RecordNotFoundException('Event not found'),
      );

      // Mark as completed
      event.markCompleted(
        completedBy: completedBy,
        completionNotes: completionNotes,
        actualCost: actualCost,
      );

      // Validate the completed event
      await _validationService.validateEvent(event, isUpdate: true);

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () => _eventsRepository.saveEvent(event),
      );

      debugPrint('✅ EVENTS CONTROLLER: Event completion saved, stream will handle UI update...');
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to complete event: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'completing event');
      rethrow;
    }
  }

  /// Delete event with error handling and cascade deletion
  Future<void> deleteEvent(String businessId) async {
    final operationId = 'delete_event_$businessId';
    
    try {
      debugPrint('🎯 EVENTS CONTROLLER: Deleting event with business ID: $businessId');
      
      // Find the event by businessId first
      final event = _unfilteredEvents.firstWhere(
        (e) => e.businessId == businessId,
        orElse: () => throw RecordNotFoundException('Event not found'),
      );

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () async {
          // Delete associated attachments first
          await _eventsRepository.deleteEventAttachmentsByEventId(businessId);
          
          // Delete the event
          await _eventsRepository.deleteEvent(event.id);
        },
      );

      debugPrint('✅ EVENTS CONTROLLER: Delete completed, stream will handle UI update...');
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to delete event: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'deleting event');
      rethrow;
    }
  }

  /// Delete event type with error handling
  Future<void> deleteEventType(String businessId) async {
    final operationId = 'delete_event_type_$businessId';
    
    try {
      debugPrint('🎯 EVENTS CONTROLLER: Deleting event type with business ID: $businessId');
      
      // Find the event type by businessId first
      final eventType = _unfilteredEventTypes.firstWhere(
        (et) => et.businessId == businessId,
        orElse: () => throw RecordNotFoundException('Event type not found'),
      );

      // Execute with retry mechanism
      await _errorHandlingService.executeWithRetry(
        operationId,
        () => _eventsRepository.deleteEventType(eventType.id),
      );

      debugPrint('✅ EVENTS CONTROLLER: Event type delete completed, stream will handle UI update...');
      
    } catch (e, stackTrace) {
      _logger.severe('Failed to delete event type: $e', e, stackTrace);
      _setState(ControllerState.error);
      _errorMessage = _errorHandlingService.handleEventError(e, context: 'deleting event type');
      rethrow;
    }
  }

  // ===== OFFLINE SUPPORT =====

  /// Add event with offline support
  Future<void> addEventWithOfflineSupport(EventIsar event) async {
    try {
      await addEvent(event);
    } catch (e) {
      // Queue for offline processing if network error
      if (_isNetworkError(e)) {
        _errorHandlingService.queueOfflineOperation('create_event', {
          'event': _eventToMap(event),
        });
        throw Exception('Event queued for offline processing');
      }
      rethrow;
    }
  }

  /// Update event with offline support
  Future<void> updateEventWithOfflineSupport(EventIsar event) async {
    try {
      await updateEvent(event);
    } catch (e) {
      // Queue for offline processing if network error
      if (_isNetworkError(e)) {
        _errorHandlingService.queueOfflineOperation('update_event', {
          'event': _eventToMap(event),
        });
        throw Exception('Event update queued for offline processing');
      }
      rethrow;
    }
  }

  /// Delete event with offline support
  Future<void> deleteEventWithOfflineSupport(String businessId) async {
    try {
      await deleteEvent(businessId);
    } catch (e) {
      // Queue for offline processing if network error
      if (_isNetworkError(e)) {
        _errorHandlingService.queueOfflineOperation('delete_event', {
          'businessId': businessId,
        });
        throw Exception('Event deletion queued for offline processing');
      }
      rethrow;
    }
  }

  /// Process offline queue
  Future<void> processOfflineQueue() async {
    try {
      await _errorHandlingService.processOfflineQueue();
    } catch (e) {
      _logger.warning('Failed to process offline queue: $e');
    }
  }

  /// Check if error is network-related
  bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('socket') ||
           errorString.contains('timeout');
  }

  /// Convert event to map for offline storage
  Map<String, dynamic> _eventToMap(EventIsar event) {
    return {
      'businessId': event.businessId,
      'title': event.title,
      'description': event.description,
      'notes': event.notes,
      'scheduledDate': event.scheduledDate?.toIso8601String(),
      'completedDate': event.completedDate?.toIso8601String(),
      'cattleTagId': event.cattleTagId,
      'eventTypeId': event.eventTypeId,
      'category': event.category.name,
      'status': event.status.name,
      'priority': event.priority.name,
      'isAutoGenerated': event.isAutoGenerated,
      'sourceModule': event.sourceModule,
      'sourceRecordId': event.sourceRecordId,
      'isRecurring': event.isRecurring,
      'recurrencePattern': event.recurrencePattern.name,
      'recurrenceInterval': event.recurrenceInterval,
      'recurrenceEndDate': event.recurrenceEndDate?.toIso8601String(),
      'parentEventId': event.parentEventId,
      'notificationsEnabled': event.notificationsEnabled,
      'reminderMinutes': event.reminderMinutes,
      'estimatedCost': event.estimatedCost,
      'actualCost': event.actualCost,
      'location': event.location,
      'weatherConditions': event.weatherConditions,
      'completedBy': event.completedBy,
      'completionNotes': event.completionNotes,
      'createdAt': event.createdAt?.toIso8601String(),
      'updatedAt': event.updatedAt?.toIso8601String(),
    };
  }

  // ===== DATA INTEGRITY VALIDATION =====

  /// Validate data integrity
  Future<List<String>> validateDataIntegrity() async {
    try {
      return await _validationService.validateEventDataIntegrity();
    } catch (e) {
      _logger.severe('Failed to validate data integrity: $e');
      return ['Failed to validate data integrity: ${e.toString()}'];
    }
  }

  /// Validate multiple events
  Future<Map<String, List<String>>> validateMultipleEvents(List<EventIsar> events) async {
    try {
      return await _validationService.validateMultipleEvents(events);
    } catch (e) {
      _logger.severe('Failed to validate multiple events: $e');
      return {'error': ['Failed to validate events: ${e.toString()}']};
    }
  }

  // ===== CLEANUP =====
  // Dispose method is already defined above

  // Helper methods
  EventIsar? getEvent(String? businessId) {
    if (businessId == null) return null;
    try {
      return _unfilteredEvents.firstWhere(
        (event) => event.businessId == businessId,
      );
    } catch (e) {
      return null;
    }
  }

  EventTypeIsar? getEventType(String? businessId) {
    if (businessId == null) return null;
    try {
      return _unfilteredEventTypes.firstWhere(
        (eventType) => eventType.businessId == businessId,
      );
    } catch (e) {
      return null;
    }
  }

  String getEventTypeName(String? eventTypeId) {
    final eventType = getEventType(eventTypeId);
    return eventType?.name ?? 'Unknown Event Type';
  }

  CattleIsar? getCattle(String? cattleTagId) {
    if (cattleTagId == null) return null;
    try {
      return _unfilteredCattle.firstWhere(
        (cattle) => cattle.tagId == cattleTagId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleTagId) {
    final cattle = getCattle(cattleTagId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  /// Get events for a specific date
  List<EventIsar> getEventsForDate(DateTime date) {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);
    
    return _filteredEvents.where((event) {
      if (event.scheduledDate == null) return false;
      return event.scheduledDate!.isAfter(startOfDay.subtract(const Duration(seconds: 1))) &&
             event.scheduledDate!.isBefore(endOfDay.add(const Duration(seconds: 1)));
    }).toList();
  }

  /// Get events for a date range
  List<EventIsar> getEventsForDateRange(DateTime startDate, DateTime endDate) {
    return _filteredEvents.where((event) {
      if (event.scheduledDate == null) return false;
      return event.scheduledDate!.isAfter(startDate.subtract(const Duration(days: 1))) &&
             event.scheduledDate!.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// Get events by category
  List<EventIsar> getEventsByCategory(EventCategory category) {
    return _filteredEvents.where((event) => event.category == category).toList();
  }

  /// Get events by status
  List<EventIsar> getEventsByStatus(EventStatus status) {
    return _filteredEvents.where((event) => event.status == status).toList();
  }

  /// Get events by cattle
  List<EventIsar> getEventsByCattle(String cattleTagId) {
    return _filteredEvents.where((event) => event.cattleTagId == cattleTagId).toList();
  }

  /// Get events by priority
  List<EventIsar> getEventsByPriority(EventPriority priority) {
    return _filteredEvents.where((event) => event.priority == priority).toList();
  }

  /// Get overdue events from filtered data
  List<EventIsar> getOverdueEvents() {
    return _filteredEvents.where((event) => event.isOverdue).toList();
  }

  /// Get upcoming events from filtered data
  List<EventIsar> getUpcomingEvents() {
    return _filteredEvents.where((event) => event.isUpcoming).toList();
  }

  /// Get auto-generated events from filtered data
  List<EventIsar> getAutoGeneratedEvents() {
    return _filteredEvents.where((event) => event.isAutoGenerated == true).toList();
  }

  /// Get manual events from filtered data
  List<EventIsar> getManualEvents() {
    return _filteredEvents.where((event) => event.isAutoGenerated != true).toList();
  }

  /// Get recurring events from filtered data
  List<EventIsar> getRecurringEvents() {
    return _filteredEvents.where((event) => event.isRecurring == true).toList();
  }

  /// Get events by location
  List<EventIsar> getEventsByLocation(String location) {
    return _filteredEvents.where((event) => 
        event.location?.toLowerCase().contains(location.toLowerCase()) == true).toList();
  }

  /// Search events by text (works on filtered data)
  List<EventIsar> searchEvents(String searchTerm) {
    if (searchTerm.trim().isEmpty) return _filteredEvents;
    
    final term = searchTerm.toLowerCase().trim();
    return _filteredEvents.where((event) {
      return (event.title?.toLowerCase().contains(term) == true) ||
             (event.description?.toLowerCase().contains(term) == true) ||
             (event.notes?.toLowerCase().contains(term) == true) ||
             (event.cattleTagId?.toLowerCase().contains(term) == true) ||
             (event.location?.toLowerCase().contains(term) == true) ||
             (event.completionNotes?.toLowerCase().contains(term) == true) ||
             (event.completedBy?.toLowerCase().contains(term) == true);
    }).toList();
  }

  /// Get filter summary for UI display
  Map<String, dynamic> getFilterSummary() {
    final summary = <String, dynamic>{};
    
    if (_currentFilters.searchQuery?.isNotEmpty == true) {
      summary['search'] = _currentFilters.searchQuery;
    }
    
    if (_currentFilters.startDate != null || _currentFilters.endDate != null) {
      summary['dateRange'] = {
        'start': _currentFilters.startDate,
        'end': _currentFilters.endDate,
      };
    }
    
    if (_currentFilters.category != null) {
      summary['category'] = _currentFilters.category!.displayName;
    }
    
    if (_currentFilters.status != null) {
      summary['status'] = _currentFilters.status!.displayName;
    }
    
    if (_currentFilters.priority != null) {
      summary['priority'] = _currentFilters.priority!.displayName;
    }
    
    if (_currentFilters.cattleTagId?.isNotEmpty == true) {
      summary['cattle'] = getCattleName(_currentFilters.cattleTagId);
    }
    
    if (_currentFilters.eventTypeId?.isNotEmpty == true) {
      summary['eventType'] = getEventTypeName(_currentFilters.eventTypeId);
    }
    
    if (_currentFilters.isAutoGenerated != null) {
      summary['automation'] = _currentFilters.isAutoGenerated! ? 'Auto-generated' : 'Manual';
    }
    
    if (_currentFilters.isRecurring != null) {
      summary['recurrence'] = _currentFilters.isRecurring! ? 'Recurring' : 'One-time';
    }
    
    return summary;
  }

  /// Get active filter count for UI badges
  int get activeFilterCount {
    int count = 0;
    
    if (_currentFilters.searchQuery?.isNotEmpty == true) count++;
    if (_currentFilters.startDate != null) count++;
    if (_currentFilters.endDate != null) count++;
    if (_currentFilters.category != null) count++;
    if (_currentFilters.status != null) count++;
    if (_currentFilters.priority != null) count++;
    if (_currentFilters.cattleTagId?.isNotEmpty == true) count++;
    if (_currentFilters.eventTypeId?.isNotEmpty == true) count++;
    if (_currentFilters.eventType?.isNotEmpty == true) count++;
    if (_currentFilters.isAutoGenerated != null) count++;
    if (_currentFilters.isRecurring != null) count++;
    if (_currentFilters.location?.isNotEmpty == true) count++;
    if (_currentFilters.completedBy?.isNotEmpty == true) count++;
    
    return count;
  }
}