import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/events_controller.dart';
import '../models/event_enums.dart';
import '../tabs/event_calendar_tab.dart';
import '../tabs/event_list_tab.dart';
import '../tabs/event_analytics_tab.dart';
import '../dialogs/event_form_dialog.dart';
import '../services/event_performance_service.dart';
import '../../../utils/message_utils.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum
import '../../User Account/guards/demo_guard.dart';

// Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart';

/// Events screen with Provider-managed controller lifecycle
/// Following the BreedingScreen pattern: StatelessWidget with ChangeNotifierProvider
class EventsScreen extends StatelessWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get navigation arguments if any
    final arguments = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    
    return ChangeNotifierProvider(
      create: (_) {
        final controller = EventsController();
        
        // Apply filters based on navigation arguments
        if (arguments != null) {
          if (arguments['cattleFilter'] != null) {
            controller.applyCattleFilter(arguments['cattleFilter'] as String);
          }
          if (arguments['moduleFilter'] != null) {
            final moduleFilter = arguments['moduleFilter'] as String;
            switch (moduleFilter.toLowerCase()) {
              case 'health':
                controller.applyCategoryFilter(EventCategory.health);
                break;
              case 'breeding':
                controller.applyCategoryFilter(EventCategory.breeding);
                break;
              case 'feeding':
                controller.applyCategoryFilter(EventCategory.feeding);
                break;
              case 'management':
                controller.applyCategoryFilter(EventCategory.management);
                break;
              case 'financial':
                controller.applyCategoryFilter(EventCategory.financial);
                break;
            }
          }
        }
        
        return controller;
      },
      child: const _EventsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _EventsScreenContent extends StatefulWidget {
  const _EventsScreenContent();

  @override
  State<_EventsScreenContent> createState() => _EventsScreenContentState();
}

class _EventsScreenContentState extends State<_EventsScreenContent>
    with TickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: 0, // Explicitly start at leftmost tab (Calendar)
    );
    
    // Initialize performance service
    EventPerformanceService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    // Note: Don't dispose EventPerformanceService here as it's shared across the app
    super.dispose();
  }

  /// Real-time loading with automatic dialog opening when ready
  void _showLoadingAndWaitForData(EventsController controller) {
    // Show immediate loading feedback
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('Loading event data...'),
          ],
        ),
        duration: Duration(milliseconds: 500), // Very short - real-time feel
      ),
    );

    // Listen for state changes and auto-open dialog when ready
    void listener() {
      if (!mounted) return;

      if (controller.state == ControllerState.loaded) {
        controller.removeListener(listener);

        // Data is ready - open event form dialog
        _openEventFormDialog(controller);
      }
    }

    controller.addListener(listener);

    // Safety timeout - remove listener after 5 seconds if still loading
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        controller.removeListener(listener);
      }
    });
  }

  void _showAddEventDialog() {
    // Check if feature is restricted in demo mode
    if (DemoGuard.isFeatureRestricted('add_event')) {
      DemoGuard.showDemoRestrictionMessage(context, 'add_event');
      return;
    }

    final eventsController = context.read<EventsController>();

    // Real-time check - immediate response
    if (eventsController.state == ControllerState.loading) {
      _showLoadingAndWaitForData(eventsController);
      return;
    }

    _openEventFormDialog(eventsController);
  }

  void _openEventFormDialog(EventsController eventsController) {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattle: eventsController.unfilteredCattle,
        eventTypes: eventsController.unfilteredEventTypes,
        onSave: (event) async {
          try {
            debugPrint('🏠 EVENTS SCREEN: Received event from form dialog');
            debugPrint('   Event ID: ${event.businessId}');
            debugPrint('   Title: ${event.title}');

            await eventsController.addEvent(event);
            debugPrint('✅ EVENTS SCREEN: Event added to controller');

            if (!mounted) return;

            // Use standardized success message
            if (context.mounted) {
              EventsMessageUtils.showSuccess(context,
                  EventsMessageUtils.eventCreated());
            }

          } catch (e) {
            debugPrint('Error adding event: $e');

            if (!mounted) return;

            // Use standardized error message
            if (context.mounted) {
              EventsMessageUtils.showError(context, 'Error adding event');
            }
          }
        },
      ),
    );
  }

  void _getCurrentTabAction() {
    switch (_tabController.index) {
      case 0:
        // Calendar tab - show add event dialog
        _showAddEventDialog();
        break;
      case 1:
        // List tab - show add event dialog
        _showAddEventDialog();
        break;
      case 2:
        // Analytics tab - no FAB action
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Event Management',
      body: Consumer<EventsController>(
        builder: (context, eventsController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.threeTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => EventCalendarTab(controller: eventsController), // Calendar tab
              ),
              Builder(
                builder: (context) => EventListTab(controller: eventsController), // List tab
              ),
              Builder(
                builder: (context) => EventAnalyticsTab(controller: eventsController), // Analytics tab
              ),
            ],
            labels: const ['Calendar', 'List', 'Analytics'],
            icons: const [Icons.calendar_month, Icons.list_alt, Icons.analytics],
            showFABs: const [true, true, false], // FAB enabled for Calendar and List tabs
          );

          // Handle different states
          if (eventsController.state == ControllerState.loading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (eventsController.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    eventsController.errorMessage ?? 'Failed to load event data',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {}, // No manual retry needed - reactive streams auto-recover
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return _tabManager!;
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            // TODO: Implement search functionality
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Search functionality coming soon'),
                duration: Duration(seconds: 2),
              ),
            );
          },
          tooltip: 'Search Events',
        ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: () {
            // TODO: Implement filter functionality
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Filter functionality coming soon'),
                duration: Duration(seconds: 2),
              ),
            );
          },
          tooltip: 'Filter Events',
        ),
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () {
            // TODO: Navigate to events report screen when implemented
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Event reports coming soon'),
                duration: Duration(seconds: 2),
              ),
            );
          },
          tooltip: 'View Event Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _getCurrentTabAction,
            tooltip: 'Add Event',
            backgroundColor: AppColors.eventsHeader,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}

/// Events module specific messages
class EventsMessageUtils extends MessageUtils {
  // ===== INHERITED METHODS =====

  /// Show a standardized success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showSuccess(context, message, duration: duration);
  }

  /// Show a standardized error message
  static void showError(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showError(context, message, duration: duration);
  }

  /// Show a standardized warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showWarning(context, message, duration: duration);
  }

  /// Show a standardized info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    MessageUtils.showInfo(context, message, duration: duration);
  }

  // ===== EVENT RECORD MESSAGES =====

  /// Event record creation messages
  static String eventCreated() => 'Event created successfully';
  static String eventUpdated() => 'Event updated successfully';
  static String eventDeleted() => 'Event deleted successfully';
  static String eventCompleted() => 'Event marked as completed';

  // ===== EVENT-SPECIFIC VALIDATION MESSAGES =====

  static String get titleRequired => 'Event title is required';
  static String get dateRequired => 'Event date is required';
  static String get cattleRequired => 'Please select cattle for this event';
  static String get eventTypeRequired => 'Event type is required';

  // ===== EVENT DELETE CONFIRMATIONS =====

  static Future<bool?> showEventDeleteConfirmation(
    BuildContext context, {
    String? eventTitle,
    String? eventId,
    List<String> specificRecords = const [],
  }) async {
    return MessageUtils.showDeleteConfirmation(
      context,
      recordType: 'event',
      itemName: eventTitle,
      recordId: eventId,
      relatedRecords: specificRecords,
    );
  }
}