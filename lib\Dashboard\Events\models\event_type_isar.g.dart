// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_type_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetEventTypeIsarCollection on Isar {
  IsarCollection<EventTypeIsar> get eventTypeIsars => this.collection();
}

const EventTypeIsarSchema = CollectionSchema(
  name: r'EventTypeIsar',
  id: 6307332385900501500,
  properties: {
    r'autoGenerationRules': PropertySchema(
      id: 0,
      name: r'autoGenerationRules',
      type: IsarType.string,
    ),
    r'businessId': PropertySchema(
      id: 1,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'canBeAutoGenerated': PropertySchema(
      id: 2,
      name: r'canBeAutoGenerated',
      type: IsarType.bool,
    ),
    r'category': PropertySchema(
      id: 3,
      name: r'category',
      type: IsarType.byte,
      enumMap: _EventTypeIsarcategoryEnumValueMap,
    ),
    r'colorHex': PropertySchema(
      id: 4,
      name: r'colorHex',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 5,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'defaultDurationMinutes': PropertySchema(
      id: 6,
      name: r'defaultDurationMinutes',
      type: IsarType.long,
    ),
    r'defaultPriority': PropertySchema(
      id: 7,
      name: r'defaultPriority',
      type: IsarType.byte,
      enumMap: _EventTypeIsardefaultPriorityEnumValueMap,
    ),
    r'defaultRecurrencePattern': PropertySchema(
      id: 8,
      name: r'defaultRecurrencePattern',
      type: IsarType.byte,
      enumMap: _EventTypeIsardefaultRecurrencePatternEnumValueMap,
    ),
    r'defaultReminderMinutes': PropertySchema(
      id: 9,
      name: r'defaultReminderMinutes',
      type: IsarType.longList,
    ),
    r'description': PropertySchema(
      id: 10,
      name: r'description',
      type: IsarType.string,
    ),
    r'iconName': PropertySchema(
      id: 11,
      name: r'iconName',
      type: IsarType.string,
    ),
    r'isActive': PropertySchema(
      id: 12,
      name: r'isActive',
      type: IsarType.bool,
    ),
    r'name': PropertySchema(
      id: 13,
      name: r'name',
      type: IsarType.string,
    ),
    r'supportsRecurrence': PropertySchema(
      id: 14,
      name: r'supportsRecurrence',
      type: IsarType.bool,
    ),
    r'updatedAt': PropertySchema(
      id: 15,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _eventTypeIsarEstimateSize,
  serialize: _eventTypeIsarSerialize,
  deserialize: _eventTypeIsarDeserialize,
  deserializeProp: _eventTypeIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _eventTypeIsarGetId,
  getLinks: _eventTypeIsarGetLinks,
  attach: _eventTypeIsarAttach,
  version: '3.1.0+1',
);

int _eventTypeIsarEstimateSize(
  EventTypeIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.autoGenerationRules;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.colorHex;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.defaultReminderMinutes;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.description;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.iconName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _eventTypeIsarSerialize(
  EventTypeIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.autoGenerationRules);
  writer.writeString(offsets[1], object.businessId);
  writer.writeBool(offsets[2], object.canBeAutoGenerated);
  writer.writeByte(offsets[3], object.category.index);
  writer.writeString(offsets[4], object.colorHex);
  writer.writeDateTime(offsets[5], object.createdAt);
  writer.writeLong(offsets[6], object.defaultDurationMinutes);
  writer.writeByte(offsets[7], object.defaultPriority.index);
  writer.writeByte(offsets[8], object.defaultRecurrencePattern.index);
  writer.writeLongList(offsets[9], object.defaultReminderMinutes);
  writer.writeString(offsets[10], object.description);
  writer.writeString(offsets[11], object.iconName);
  writer.writeBool(offsets[12], object.isActive);
  writer.writeString(offsets[13], object.name);
  writer.writeBool(offsets[14], object.supportsRecurrence);
  writer.writeDateTime(offsets[15], object.updatedAt);
}

EventTypeIsar _eventTypeIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = EventTypeIsar();
  object.autoGenerationRules = reader.readStringOrNull(offsets[0]);
  object.businessId = reader.readStringOrNull(offsets[1]);
  object.canBeAutoGenerated = reader.readBoolOrNull(offsets[2]);
  object.category =
      _EventTypeIsarcategoryValueEnumMap[reader.readByteOrNull(offsets[3])] ??
          EventCategory.health;
  object.colorHex = reader.readStringOrNull(offsets[4]);
  object.createdAt = reader.readDateTimeOrNull(offsets[5]);
  object.defaultDurationMinutes = reader.readLongOrNull(offsets[6]);
  object.defaultPriority = _EventTypeIsardefaultPriorityValueEnumMap[
          reader.readByteOrNull(offsets[7])] ??
      EventPriority.low;
  object.defaultRecurrencePattern =
      _EventTypeIsardefaultRecurrencePatternValueEnumMap[
              reader.readByteOrNull(offsets[8])] ??
          RecurrencePattern.daily;
  object.defaultReminderMinutes = reader.readLongList(offsets[9]);
  object.description = reader.readStringOrNull(offsets[10]);
  object.iconName = reader.readStringOrNull(offsets[11]);
  object.id = id;
  object.isActive = reader.readBoolOrNull(offsets[12]);
  object.name = reader.readStringOrNull(offsets[13]);
  object.supportsRecurrence = reader.readBoolOrNull(offsets[14]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[15]);
  return object;
}

P _eventTypeIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readBoolOrNull(offset)) as P;
    case 3:
      return (_EventTypeIsarcategoryValueEnumMap[
              reader.readByteOrNull(offset)] ??
          EventCategory.health) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 6:
      return (reader.readLongOrNull(offset)) as P;
    case 7:
      return (_EventTypeIsardefaultPriorityValueEnumMap[
              reader.readByteOrNull(offset)] ??
          EventPriority.low) as P;
    case 8:
      return (_EventTypeIsardefaultRecurrencePatternValueEnumMap[
              reader.readByteOrNull(offset)] ??
          RecurrencePattern.daily) as P;
    case 9:
      return (reader.readLongList(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readBoolOrNull(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    case 14:
      return (reader.readBoolOrNull(offset)) as P;
    case 15:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _EventTypeIsarcategoryEnumValueMap = {
  'health': 0,
  'breeding': 1,
  'feeding': 2,
  'management': 3,
  'maintenance': 4,
  'financial': 5,
  'other': 6,
};
const _EventTypeIsarcategoryValueEnumMap = {
  0: EventCategory.health,
  1: EventCategory.breeding,
  2: EventCategory.feeding,
  3: EventCategory.management,
  4: EventCategory.maintenance,
  5: EventCategory.financial,
  6: EventCategory.other,
};
const _EventTypeIsardefaultPriorityEnumValueMap = {
  'low': 0,
  'medium': 1,
  'high': 2,
  'critical': 3,
};
const _EventTypeIsardefaultPriorityValueEnumMap = {
  0: EventPriority.low,
  1: EventPriority.medium,
  2: EventPriority.high,
  3: EventPriority.critical,
};
const _EventTypeIsardefaultRecurrencePatternEnumValueMap = {
  'daily': 0,
  'weekly': 1,
  'monthly': 2,
  'yearly': 3,
  'custom': 4,
};
const _EventTypeIsardefaultRecurrencePatternValueEnumMap = {
  0: RecurrencePattern.daily,
  1: RecurrencePattern.weekly,
  2: RecurrencePattern.monthly,
  3: RecurrencePattern.yearly,
  4: RecurrencePattern.custom,
};

Id _eventTypeIsarGetId(EventTypeIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _eventTypeIsarGetLinks(EventTypeIsar object) {
  return [];
}

void _eventTypeIsarAttach(
    IsarCollection<dynamic> col, Id id, EventTypeIsar object) {
  object.id = id;
}

extension EventTypeIsarByIndex on IsarCollection<EventTypeIsar> {
  Future<EventTypeIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  EventTypeIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<EventTypeIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<EventTypeIsar?> getAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(EventTypeIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(EventTypeIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<EventTypeIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<EventTypeIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension EventTypeIsarQueryWhereSort
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QWhere> {
  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension EventTypeIsarQueryWhere
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QWhereClause> {
  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause> idGreaterThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension EventTypeIsarQueryFilter
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QFilterCondition> {
  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'autoGenerationRules',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'autoGenerationRules',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoGenerationRules',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'autoGenerationRules',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'autoGenerationRules',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'autoGenerationRules',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'autoGenerationRules',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'autoGenerationRules',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'autoGenerationRules',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'autoGenerationRules',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoGenerationRules',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      autoGenerationRulesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'autoGenerationRules',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      canBeAutoGeneratedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'canBeAutoGenerated',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      canBeAutoGeneratedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'canBeAutoGenerated',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      canBeAutoGeneratedEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'canBeAutoGenerated',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      categoryEqualTo(EventCategory value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'category',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      categoryGreaterThan(
    EventCategory value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'category',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      categoryLessThan(
    EventCategory value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'category',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      categoryBetween(
    EventCategory lower,
    EventCategory upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'category',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'colorHex',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'colorHex',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'colorHex',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'colorHex',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'colorHex',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'colorHex',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'colorHex',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'colorHex',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'colorHex',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'colorHex',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'colorHex',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      colorHexIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'colorHex',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultDurationMinutesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'defaultDurationMinutes',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultDurationMinutesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'defaultDurationMinutes',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultDurationMinutesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultDurationMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultDurationMinutesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultDurationMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultDurationMinutesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultDurationMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultDurationMinutesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultDurationMinutes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultPriorityEqualTo(EventPriority value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultPriority',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultPriorityGreaterThan(
    EventPriority value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultPriority',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultPriorityLessThan(
    EventPriority value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultPriority',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultPriorityBetween(
    EventPriority lower,
    EventPriority upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultPriority',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultRecurrencePatternEqualTo(RecurrencePattern value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultRecurrencePattern',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultRecurrencePatternGreaterThan(
    RecurrencePattern value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultRecurrencePattern',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultRecurrencePatternLessThan(
    RecurrencePattern value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultRecurrencePattern',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultRecurrencePatternBetween(
    RecurrencePattern lower,
    RecurrencePattern upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultRecurrencePattern',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'defaultReminderMinutes',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'defaultReminderMinutes',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultReminderMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultReminderMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultReminderMinutes',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultReminderMinutes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'defaultReminderMinutes',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'defaultReminderMinutes',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'defaultReminderMinutes',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'defaultReminderMinutes',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'defaultReminderMinutes',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      defaultReminderMinutesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'defaultReminderMinutes',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'description',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'description',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      descriptionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'iconName',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'iconName',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iconName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'iconName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'iconName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'iconName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'iconName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'iconName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'iconName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'iconName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iconName',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      iconNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'iconName',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      isActiveIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isActive',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      isActiveIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isActive',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      isActiveEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isActive',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition> nameMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      supportsRecurrenceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'supportsRecurrence',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      supportsRecurrenceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'supportsRecurrence',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      supportsRecurrenceEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supportsRecurrence',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension EventTypeIsarQueryObject
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QFilterCondition> {}

extension EventTypeIsarQueryLinks
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QFilterCondition> {}

extension EventTypeIsarQuerySortBy
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QSortBy> {
  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByAutoGenerationRules() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoGenerationRules', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByAutoGenerationRulesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoGenerationRules', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByCanBeAutoGenerated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canBeAutoGenerated', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByCanBeAutoGeneratedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canBeAutoGenerated', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByColorHex() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorHex', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByColorHexDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorHex', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByDefaultDurationMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultDurationMinutes', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByDefaultDurationMinutesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultDurationMinutes', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByDefaultPriority() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultPriority', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByDefaultPriorityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultPriority', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByDefaultRecurrencePattern() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultRecurrencePattern', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByDefaultRecurrencePatternDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultRecurrencePattern', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByIconName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconName', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByIconNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconName', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByIsActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortBySupportsRecurrence() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportsRecurrence', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortBySupportsRecurrenceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportsRecurrence', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension EventTypeIsarQuerySortThenBy
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QSortThenBy> {
  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByAutoGenerationRules() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoGenerationRules', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByAutoGenerationRulesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoGenerationRules', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByCanBeAutoGenerated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canBeAutoGenerated', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByCanBeAutoGeneratedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canBeAutoGenerated', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'category', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByColorHex() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorHex', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByColorHexDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorHex', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByDefaultDurationMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultDurationMinutes', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByDefaultDurationMinutesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultDurationMinutes', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByDefaultPriority() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultPriority', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByDefaultPriorityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultPriority', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByDefaultRecurrencePattern() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultRecurrencePattern', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByDefaultRecurrencePatternDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultRecurrencePattern', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByIconName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconName', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByIconNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconName', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByIsActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenBySupportsRecurrence() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportsRecurrence', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenBySupportsRecurrenceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportsRecurrence', Sort.desc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy> thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension EventTypeIsarQueryWhereDistinct
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> {
  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct>
      distinctByAutoGenerationRules({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoGenerationRules',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByBusinessId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct>
      distinctByCanBeAutoGenerated() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'canBeAutoGenerated');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'category');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByColorHex(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'colorHex', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct>
      distinctByDefaultDurationMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultDurationMinutes');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct>
      distinctByDefaultPriority() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultPriority');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct>
      distinctByDefaultRecurrencePattern() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultRecurrencePattern');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct>
      distinctByDefaultReminderMinutes() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultReminderMinutes');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByDescription(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'description', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByIconName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'iconName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isActive');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct>
      distinctBySupportsRecurrence() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'supportsRecurrence');
    });
  }

  QueryBuilder<EventTypeIsar, EventTypeIsar, QDistinct> distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension EventTypeIsarQueryProperty
    on QueryBuilder<EventTypeIsar, EventTypeIsar, QQueryProperty> {
  QueryBuilder<EventTypeIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<EventTypeIsar, String?, QQueryOperations>
      autoGenerationRulesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoGenerationRules');
    });
  }

  QueryBuilder<EventTypeIsar, String?, QQueryOperations> businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<EventTypeIsar, bool?, QQueryOperations>
      canBeAutoGeneratedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'canBeAutoGenerated');
    });
  }

  QueryBuilder<EventTypeIsar, EventCategory, QQueryOperations>
      categoryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'category');
    });
  }

  QueryBuilder<EventTypeIsar, String?, QQueryOperations> colorHexProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'colorHex');
    });
  }

  QueryBuilder<EventTypeIsar, DateTime?, QQueryOperations> createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<EventTypeIsar, int?, QQueryOperations>
      defaultDurationMinutesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultDurationMinutes');
    });
  }

  QueryBuilder<EventTypeIsar, EventPriority, QQueryOperations>
      defaultPriorityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultPriority');
    });
  }

  QueryBuilder<EventTypeIsar, RecurrencePattern, QQueryOperations>
      defaultRecurrencePatternProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultRecurrencePattern');
    });
  }

  QueryBuilder<EventTypeIsar, List<int>?, QQueryOperations>
      defaultReminderMinutesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultReminderMinutes');
    });
  }

  QueryBuilder<EventTypeIsar, String?, QQueryOperations> descriptionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'description');
    });
  }

  QueryBuilder<EventTypeIsar, String?, QQueryOperations> iconNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'iconName');
    });
  }

  QueryBuilder<EventTypeIsar, bool?, QQueryOperations> isActiveProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isActive');
    });
  }

  QueryBuilder<EventTypeIsar, String?, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<EventTypeIsar, bool?, QQueryOperations>
      supportsRecurrenceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'supportsRecurrence');
    });
  }

  QueryBuilder<EventTypeIsar, DateTime?, QQueryOperations> updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
