import 'dart:async';
import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

import 'notification_repository.dart';
import 'notification_settings_repository.dart';
import 'push_notification_service.dart';
import 'offline_notification_service.dart';
import '../models/notification_request.dart';
import '../models/notification_filter.dart';
import '../models/notification_isar.dart';
import '../models/notification_settings_isar.dart';
import '../models/push_notification_request.dart';
import '../models/scheduled_notification_request.dart';
import '../models/notification_priority.dart';

/// Primary orchestration service for notification operations
class NotificationService {
  final Logger _logger = Logger('NotificationService');
  final NotificationRepository _repository;
  final NotificationSettingsRepository _settingsRepository;
  final PushNotificationService _pushService;
  final OfflineNotificationService _offlineService;
  final Uuid _uuid = const Uuid();
  
  /// Stream controller for notification changes
  final StreamController<void> _notificationChangesController = StreamController<void>.broadcast();
  
  /// Stream of notification changes
  Stream<void> get onNotificationChanges => _notificationChangesController.stream;
  
  /// Constructor with dependencies
  NotificationService(
    this._repository,
    this._settingsRepository,
    this._pushService,
    this._offlineService,
  );
  
  /// Initialize the service
  Future<void> initialize() async {
    try {
      // Initialize push service
      await _pushService.initialize();
      
      // Initialize offline service
      await _offlineService.initialize();
      
      // Sync any pending operations
      await _offlineService.syncPendingOperations();
      
      _logger.info('NotificationService initialized');
    } catch (e) {
      _logger.severe('Error initializing NotificationService: $e');
    }
  }
  
  /// Create a notification
  Future<NotificationIsar> createNotification(NotificationRequest request) async {
    try {
      // Check connectivity
      if (await _isOffline()) {
        // Queue operation for later
        await _offlineService.queueCreateNotification(request);
        
        // Create temporary notification for immediate display
        final tempNotification = NotificationIsar(
          title: request.title,
          message: request.message,
          category: request.category,
          type: request.type,
          priority: request.priority,
          cattleId: request.cattleId,
          relatedRecordId: request.relatedRecordId,
          relatedRecordType: request.relatedRecordType,
          eventId: request.eventId,
          imageUrl: request.imageUrl,
          actionUrl: request.actionUrl,
          customData: request.customData,
          createdAt: DateTime.now(),
          businessId: _uuid.v4(),
          isSynced: false,
        );
        
        // Save locally
        final savedNotification = await _repository.saveNotification(tempNotification);
        
        // Notify listeners
        _notificationChangesController.add(null);
        
        return savedNotification;
      }
      
      // Create notification
      final notification = NotificationIsar(
        title: request.title,
        message: request.message,
        category: request.category,
        type: request.type,
        priority: request.priority,
        cattleId: request.cattleId,
        relatedRecordId: request.relatedRecordId,
        relatedRecordType: request.relatedRecordType,
        eventId: request.eventId,
        imageUrl: request.imageUrl,
        actionUrl: request.actionUrl,
        customData: request.customData,
        createdAt: DateTime.now(),
        businessId: _uuid.v4(),
      );
      
      // Save notification
      final savedNotification = await _repository.saveNotification(notification);
      
      // Check if push notification should be sent
      final settings = await _settingsRepository.getSettings();
      if (settings.pushNotificationsEnabled && 
          _shouldSendPushNotification(request.category, request.priority, settings)) {
        // Send push notification
        await _sendPushNotification(savedNotification);
      }
      
      // Notify listeners
      _notificationChangesController.add(null);
      
      return savedNotification;
    } catch (e) {
      _logger.severe('Error creating notification: $e');
      rethrow;
    }
  }
  
  /// Get notifications with filtering
  Future<List<NotificationIsar>> getNotifications({NotificationFilter? filter}) async {
    try {
      return await _repository.getNotifications(filter: filter);
    } catch (e) {
      _logger.severe('Error getting notifications: $e');
      rethrow;
    }
  }
  
  /// Get a notification by ID
  Future<NotificationIsar?> getNotificationById(String notificationId) async {
    try {
      return await _repository.getNotificationById(notificationId);
    } catch (e) {
      _logger.severe('Error getting notification by ID: $e');
      rethrow;
    }
  }
  
  /// Mark a notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      // Check connectivity
      if (await _isOffline()) {
        // Queue operation for later
        await _offlineService.queueMarkAsRead(notificationId);
        
        // Notify listeners
        _notificationChangesController.add(null);
        return;
      }
      
      // Mark as read
      await _repository.markAsRead(notificationId);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error marking notification as read: $e');
      rethrow;
    }
  }
  
  /// Mark a notification as actioned
  Future<void> markAsActioned(String notificationId) async {
    try {
      // Check connectivity
      if (await _isOffline()) {
        // Queue operation for later
        await _offlineService.queueMarkAsActioned(notificationId);
        
        // Notify listeners
        _notificationChangesController.add(null);
        return;
      }
      
      // Mark as actioned
      await _repository.markAsActioned(notificationId);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error marking notification as actioned: $e');
      rethrow;
    }
  }
  
  /// Archive a notification
  Future<void> archiveNotification(String notificationId) async {
    try {
      // Check connectivity
      if (await _isOffline()) {
        // Queue operation for later
        await _offlineService.queueArchive(notificationId);
        
        // Notify listeners
        _notificationChangesController.add(null);
        return;
      }
      
      // Archive notification
      await _repository.archiveNotification(notificationId);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error archiving notification: $e');
      rethrow;
    }
  }
  
  /// Delete a notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      // Check connectivity
      if (await _isOffline()) {
        // Queue operation for later
        await _offlineService.queueDelete(notificationId);
        
        // Notify listeners
        _notificationChangesController.add(null);
        return;
      }
      
      // Delete notification
      await _repository.deleteNotification(notificationId);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error deleting notification: $e');
      rethrow;
    }
  }
  
  /// Mark all notifications as read
  Future<void> markAllAsRead({String? category}) async {
    try {
      // Mark all as read
      await _repository.markAllAsRead(category: category);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error marking all notifications as read: $e');
      rethrow;
    }
  }
  
  /// Mark multiple notifications as read
  Future<void> markMultipleAsRead(List<String> notificationIds) async {
    try {
      // Mark multiple as read
      await _repository.markMultipleAsRead(notificationIds);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error marking multiple notifications as read: $e');
      rethrow;
    }
  }
  
  /// Delete multiple notifications
  Future<void> deleteMultiple(List<String> notificationIds) async {
    try {
      // Delete multiple
      await _repository.deleteMultiple(notificationIds);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error deleting multiple notifications: $e');
      rethrow;
    }
  }
  
  /// Get unread notification count
  Future<int> getUnreadCount({String? category}) async {
    try {
      return await _repository.getUnreadCount(category: category);
    } catch (e) {
      _logger.severe('Error getting unread notification count: $e');
      rethrow;
    }
  }
  
  /// Get notification counts by category
  Future<Map<String, int>> getNotificationCountsByCategory() async {
    try {
      return await _repository.getNotificationCountsByCategory();
    } catch (e) {
      _logger.severe('Error getting notification counts by category: $e');
      rethrow;
    }
  }
  
  /// Schedule a notification
  Future<void> scheduleNotification(ScheduledNotificationRequest request) async {
    try {
      // Create notification with scheduled date
      final notification = NotificationIsar(
        title: request.title,
        message: request.message,
        category: request.category,
        type: request.type,
        priority: request.priority,
        cattleId: request.cattleId,
        relatedRecordId: request.relatedRecordId,
        relatedRecordType: request.relatedRecordType,
        eventId: request.eventId,
        imageUrl: request.imageUrl,
        actionUrl: request.actionUrl,
        customData: request.customData,
        scheduledFor: request.scheduledFor,
        expiresAt: request.expiresAt,
        isRecurring: request.isRecurring,
        recurringPattern: request.recurringPattern,
        createdAt: DateTime.now(),
        businessId: _uuid.v4(),
      );
      
      // Save to repository
      await _repository.saveNotification(notification);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error scheduling notification: $e');
      rethrow;
    }
  }
  
  /// Cancel a scheduled notification
  Future<void> cancelScheduledNotification(String notificationId) async {
    try {
      await _repository.deleteNotification(notificationId);
      
      // Notify listeners
      _notificationChangesController.add(null);
    } catch (e) {
      _logger.severe('Error cancelling scheduled notification: $e');
      rethrow;
    }
  }
  
  /// Update notification settings
  Future<void> updateSettings(Map<String, dynamic> settings) async {
    try {
      // Get current settings
      final currentSettings = await _settingsRepository.getSettings();
      
      // Update settings
      if (settings.containsKey('notificationsEnabled')) {
        currentSettings.notificationsEnabled = settings['notificationsEnabled'];
      }
      
      if (settings.containsKey('pushNotificationsEnabled')) {
        currentSettings.pushNotificationsEnabled = settings['pushNotificationsEnabled'];
      }
      
      if (settings.containsKey('inAppNotificationsEnabled')) {
        currentSettings.inAppNotificationsEnabled = settings['inAppNotificationsEnabled'];
      }
      
      if (settings.containsKey('emailNotificationsEnabled')) {
        currentSettings.emailNotificationsEnabled = settings['emailNotificationsEnabled'];
      }
      
      if (settings.containsKey('smsNotificationsEnabled')) {
        currentSettings.smsNotificationsEnabled = settings['smsNotificationsEnabled'];
      }
      
      if (settings.containsKey('categoryEnabled')) {
        currentSettings.setCategoryEnabled(Map<String, bool>.from(settings['categoryEnabled']));
      }
      
      if (settings.containsKey('quietHoursEnabled')) {
        currentSettings.quietHoursEnabled = settings['quietHoursEnabled'];
      }
      
      if (settings.containsKey('quietHoursStart')) {
        currentSettings.quietHoursStart = settings['quietHoursStart'];
      }
      
      if (settings.containsKey('quietHoursEnd')) {
        currentSettings.quietHoursEnd = settings['quietHoursEnd'];
      }
      
      // Save updated settings
      await _settingsRepository.saveSettings(currentSettings);
    } catch (e) {
      _logger.severe('Error updating notification settings: $e');
      rethrow;
    }
  }
  
  /// Get notification settings
  Future<NotificationSettingsIsar> getSettings() async {
    try {
      return await _settingsRepository.getSettings();
    } catch (e) {
      _logger.severe('Error getting notification settings: $e');
      rethrow;
    }
  }
  
  /// Clean up old notifications based on retention settings
  Future<int> cleanupOldNotifications() async {
    try {
      // Get settings
      final settings = await _settingsRepository.getSettings();
      
      // Clean up old notifications
      return await _repository.cleanupOldNotifications(
        settings.autoDeleteAfterDays,
        settings.autoDeleteReadAfterDays,
      );
    } catch (e) {
      _logger.severe('Error cleaning up old notifications: $e');
      rethrow;
    }
  }
  
  /// Send a push notification for a notification
  Future<bool> _sendPushNotification(NotificationIsar notification) async {
    try {
      // Create push notification request
      final request = PushNotificationRequest(
        title: notification.title ?? 'Notification',
        body: notification.message ?? '',
        priority: notification.priority,
        imageUrl: notification.imageUrl,
        data: {
          'notificationId': notification.businessId ?? '',
          'category': notification.category ?? '',
          'type': notification.type ?? '',
          'cattleId': notification.cattleId ?? '',
          'relatedRecordId': notification.relatedRecordId ?? '',
          'relatedRecordType': notification.relatedRecordType ?? '',
          'eventId': notification.eventId ?? '',
        },
      );
      
      // Send push notification
      final success = await _pushService.sendPushNotification(request);
      
      if (success) {
        // Update notification with push details
        notification.pushNotificationSent = true;
        notification.pushNotificationSentAt = DateTime.now();
        await _repository.saveNotification(notification);
      }
      
      return success;
    } catch (e) {
      _logger.severe('Error sending push notification: $e');
      return false;
    }
  }
  
  /// Check if device is offline
  Future<bool> _isOffline() async {
    // This would typically check connectivity
    // For now, we'll just return false (online)
    return false;
  }
  
  /// Check if push notification should be sent based on settings
  bool _shouldSendPushNotification(String? category, NotificationPriority priority, NotificationSettingsIsar settings) {
    // Check if push notifications are enabled
    if (!settings.pushNotificationsEnabled) {
      return false;
    }
    
    // Check if category is enabled
    if (category != null) {
      final categoryEnabled = settings.getCategoryEnabled();
      if (!categoryEnabled.containsKey(category) || !categoryEnabled[category]!) {
        return false;
      }
    }
    
    // Check quiet hours
    if (settings.quietHoursEnabled) {
      final now = DateTime.now();
      final currentHour = now.hour;
      
      // If within quiet hours
      if (_isWithinQuietHours(currentHour, settings.quietHoursStart, settings.quietHoursEnd)) {
        // Check if emergency override applies
        if (settings.emergencyOverrideEnabled && 
            (priority == NotificationPriority.high || priority == NotificationPriority.critical)) {
          return true;
        }
        
        return false;
      }
    }
    
    return true;
  }
  
  /// Check if current hour is within quiet hours
  bool _isWithinQuietHours(int currentHour, int start, int end) {
    if (start <= end) {
      // Simple case: start time is before end time
      return currentHour >= start && currentHour < end;
    } else {
      // Wrap around case: start time is after end time (e.g., 22:00 to 7:00)
      return currentHour >= start || currentHour < end;
    }
  }
  
  /// Dispose resources
  void dispose() {
    _notificationChangesController.close();
    _pushService.dispose();
    _offlineService.dispose();
  }
}