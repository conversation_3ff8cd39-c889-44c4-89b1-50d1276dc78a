
import 'package:flutter/material.dart';
import '../models/report_models.dart';
import '../services/reports_service.dart';
import '../services/reports_cache_service.dart';
import '../services/performance_monitor.dart';
import '../services/error_handler.dart';
import '../services/sharing_service.dart';
import '../../../core/get_stub.dart';

/// Reports Controller
/// 
/// Manages state for the unified reports system, handles data loading,
/// filtering, and export operations.
class ReportsController extends GetxController {
  final ReportsService _reportsService = ReportsService();
  final ReportsCacheService _cacheService = ReportsCacheService();
  final SharingService _sharingService = SharingService();

  // Observable state
  final isLoading = false.obs;
  final errorMessage = ''.obs;
  final dashboardData = Rxn<ReportData>();
  final currentReportData = Rxn<ReportData>();
  final selectedReportType = ReportType.dashboard.obs;
  final currentFilter = const FilterState().obs;

  @override
  void onInit() {
    super.onInit();
    loadDashboard();
  }

  /// Load dashboard data with performance monitoring and error handling
  Future<void> loadDashboard() async {
    await PerformanceMonitor.measureLoadTime(
      'Dashboard Load',
      () async {
        try {
          isLoading.value = true;
          errorMessage.value = '';

          // Validate filter first
          final filterValidation = ReportsErrorHandler.validateFilterState(currentFilter.value);
          if (!filterValidation.isValid) {
            errorMessage.value = filterValidation.firstError;
            dashboardData.value = ReportsErrorHandler.handleReportError(
              filterValidation.firstError,
              ReportType.dashboard,
              retryCallback: loadDashboard,
            );
            return;
          }

          // Check cache first with error handling
          final cachedData = ReportsErrorHandler.handleCacheError(
            'Dashboard Cache Read',
            () => _cacheService.getCachedReport(ReportType.dashboard, currentFilter.value),
            () => null,
          );

          if (cachedData != null) {
            dashboardData.value = cachedData;
            isLoading.value = false;
            PerformanceMonitor.checkMemoryUsage('Dashboard Cache Hit');
            return;
          }

          // Load fresh data
          final data = await _reportsService.getDashboardReport(currentFilter.value);

          // Validate report data
          final reportValidation = ReportsErrorHandler.validateReportData(data);
          if (!reportValidation.isValid) {
            throw Exception(reportValidation.firstError);
          }

          dashboardData.value = data;

          // Cache the result with error handling
          ReportsErrorHandler.handleCacheError(
            'Dashboard Cache Write',
            () {
              _cacheService.cacheReport(ReportType.dashboard, currentFilter.value, data);
              return true;
            },
            () => false,
          );

          // Log warnings if any
          if (reportValidation.hasWarnings) {
            errorMessage.value = reportValidation.firstWarning;
          }

        } catch (e) {
          errorMessage.value = ReportsErrorHandler.getUserFriendlyMessage(e);
          dashboardData.value = ReportsErrorHandler.handleReportError(
            e,
            ReportType.dashboard,
            context: 'Dashboard loading failed',
            retryCallback: loadDashboard,
          );
        } finally {
          isLoading.value = false;
        }
      },
    );
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard() async {
    _cacheService.invalidateReportType(ReportType.dashboard);
    await loadDashboard();
  }

  /// Select report type and load data
  Future<void> selectReportType(ReportType type) async {
    if (selectedReportType.value == type) return;
    
    selectedReportType.value = type;
    await loadCurrentReport();
  }

  /// Load current report data
  Future<void> loadCurrentReport() async {
    try {
      isLoading.value = true;
      
      // Check cache first
      final cachedData = _cacheService.getCachedReport(
        selectedReportType.value, 
        currentFilter.value,
      );
      
      if (cachedData != null) {
        currentReportData.value = cachedData;
        isLoading.value = false;
        return;
      }
      
      // Load fresh data based on report type
      ReportData data;
      switch (selectedReportType.value) {
        case ReportType.dashboard:
          data = await _reportsService.getDashboardReport(currentFilter.value);
          break;
        case ReportType.cattle:
          data = await _reportsService.getCattleReport(currentFilter.value);
          break;
        case ReportType.milk:
          data = await _reportsService.getMilkReport(currentFilter.value);
          break;
        case ReportType.health:
          data = await _reportsService.getHealthReport(currentFilter.value);
          break;
        case ReportType.breeding:
          // Placeholder - implement when breeding service is ready
          data = ReportData.empty(ReportType.breeding);
          break;
        case ReportType.weight:
          // Placeholder - implement when weight service is ready
          data = ReportData.empty(ReportType.weight);
          break;
        case ReportType.financial:
          data = await _reportsService.getFinancialReport(currentFilter.value);
          break;
      }
      
      currentReportData.value = data;
      
      // Cache the result
      _cacheService.cacheReport(selectedReportType.value, currentFilter.value, data);
      
    } catch (e) {
      currentReportData.value = ReportData.empty(selectedReportType.value);
      Get.snackbar('Error', 'Failed to load report: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Update date range filter
  Future<void> updateDateRange(DateTime startDate, DateTime endDate) async {
    final newFilter = currentFilter.value.copyWith(
      startDate: startDate,
      endDate: endDate,
    );
    
    currentFilter.value = newFilter;
    
    // Use selective cache invalidation for better performance
    _cacheService.invalidateSelective(ReportType.dashboard, newFilter);
    
    // Reload current data
    await Future.wait([
      loadDashboard(),
      loadCurrentReport(),
    ]);
  }

  /// Update cattle filter
  Future<void> updateCattleFilter(List<String> cattleIds) async {
    final newFilter = currentFilter.value.copyWith(cattleIds: cattleIds);
    currentFilter.value = newFilter;
    
    // Use selective cache invalidation for better performance
    _cacheService.invalidateSelective(ReportType.dashboard, newFilter);
    await Future.wait([
      loadDashboard(),
      loadCurrentReport(),
    ]);
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    currentFilter.value = const FilterState();
    
    // Clear all cache when clearing filters
    _cacheService.invalidateAll();
    await Future.wait([
      loadDashboard(),
      loadCurrentReport(),
    ]);
  }

  /// Export dashboard as PDF
  Future<void> exportDashboardPDF() async {
    final data = dashboardData.value;
    if (data == null) {
      Get.snackbar('Error', 'No dashboard data to export');
      return;
    }
    
    await _sharingService.downloadPDF(data);
  }

  /// Export dashboard as Excel
  Future<void> exportDashboardExcel() async {
    final data = dashboardData.value;
    if (data == null) {
      Get.snackbar('Error', 'No dashboard data to export');
      return;
    }
    
    await _sharingService.downloadExcel(data);
  }

  /// Export current report
  Future<void> exportCurrentReport(ExportFormat format, ExportType type) async {
    final data = currentReportData.value ?? dashboardData.value;
    if (data == null) {
      Get.snackbar('Error', 'No report data to export');
      return;
    }
    
    final config = format == ExportFormat.pdf 
        ? ExportConfig.pdf(type: type)
        : ExportConfig.excel(type: type);
    
    switch (type) {
      case ExportType.download:
        if (format == ExportFormat.pdf) {
          await _sharingService.downloadPDF(data, config: config);
        } else {
          await _sharingService.downloadExcel(data, config: config);
        }
        break;
      case ExportType.share:
        if (format == ExportFormat.pdf) {
          await _sharingService.sharePDF(data, config: config);
        } else {
          await _sharingService.shareExcel(data, config: config);
        }
        break;
      case ExportType.email:
        if (format == ExportFormat.pdf) {
          await _sharingService.emailPDF(data, config: config);
        } else {
          await _sharingService.emailExcel(data, config: config);
        }
        break;
      case ExportType.print:
        await _sharingService.printPDF(data, config: config);
        break;
    }
  }

  /// Batch export all reports
  Future<void> batchExportAll() async {
    try {
      isLoading.value = true;
      
      // Generate all report types
      final reports = <ReportData>[];
      
      for (final type in ReportType.values) {
        try {
          ReportData data;
          switch (type) {
            case ReportType.dashboard:
              data = await _reportsService.getDashboardReport(currentFilter.value);
              break;
            case ReportType.cattle:
              data = await _reportsService.getCattleReport(currentFilter.value);
              break;
            case ReportType.milk:
              data = await _reportsService.getMilkReport(currentFilter.value);
              break;
            case ReportType.health:
              data = await _reportsService.getHealthReport(currentFilter.value);
              break;
            case ReportType.financial:
              data = await _reportsService.getFinancialReport(currentFilter.value);
              break;
            default:
              data = ReportData.empty(type);
          }
          
          if (data.hasData) {
            reports.add(data);
          }
        } catch (e) {
          // Skip failed reports
          continue;
        }
      }
      
      if (reports.isEmpty) {
        Get.snackbar('Error', 'No reports available for export');
        return;
      }

      // Export each report as PDF
      for (final report in reports) {
        await _sharingService.downloadPDF(report);
      }

      Get.snackbar(
        'Batch Export Complete',
        '${reports.length} reports exported successfully',
        duration: const Duration(seconds: 4),
      );

    } catch (e) {
      Get.snackbar('Error', 'Batch export failed: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Get cache statistics
  String getCacheStats() {
    final stats = _cacheService.getCacheStats();
    return stats.toString();
  }

  /// Clear cache
  void clearCache() {
    _cacheService.invalidateAll();
    Get.snackbar('Cache Cleared', 'All cached reports have been cleared');
  }
}