import 'package:isar/isar.dart';
import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

import '../../../services/database/isar_service.dart';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';

/// Repository for notification operations with Isar integration
class NotificationRepository {
  final Logger _logger = Logger('NotificationRepository');
  final IsarService _isarService;
  final Uuid _uuid = const Uuid();

  /// Constructor with IsarService dependency
  NotificationRepository(this._isarService);

  /// Save a notification to the database
  Future<NotificationIsar> saveNotification(NotificationIsar notification) async {
    try {
      // Generate business ID if not provided
      if (notification.businessId == null || notification.businessId!.isEmpty) {
        notification.businessId = _uuid.v4();
      }

      // Set timestamps if not provided
      notification.createdAt ??= DateTime.now();
      notification.updatedAt = DateTime.now();

      // Prepare for saving (serialize JSON fields)
      notification.prepareForSave();

      // Save to database
      await _isarService.isar.writeTxn(() async {
        await _isarService.notificationIsars.put(notification);
      });

      _logger.info('Saved notification: ${notification.businessId}');
      return notification;
    } catch (e) {
      _logger.severe('Error saving notification: $e');
      rethrow;
    }
  }

  /// Get a notification by its business ID
  Future<NotificationIsar?> getNotificationById(String businessId) async {
    try {
      final notification = await _isarService.notificationIsars
          .getByBusinessId(businessId);

      if (notification != null) {
        // Process after loading (deserialize JSON fields)
        notification.processAfterLoad();
      }

      return notification;
    } catch (e) {
      _logger.severe('Error getting notification by ID: $e');
      rethrow;
    }
  }

  /// Get notifications with optional filtering, pagination, and sorting
  Future<List<NotificationIsar>> getNotifications({
    NotificationFilter? filter,
    int? limit,
    int? offset,
    bool sortByDateDesc = true,
  }) async {
    try {
      // Start with a base query
      var query = _isarService.notificationIsars.filter();

      // Apply filters if provided
      if (filter != null) {
        if (filter.status != null) {
          query = query.statusEqualTo(filter.status!);
        }

        if (filter.category != null && filter.category!.isNotEmpty) {
          query = query.categoryEqualTo(filter.category!);
        }

        if (filter.type != null && filter.type!.isNotEmpty) {
          query = query.typeEqualTo(filter.type!);
        }

        if (filter.priority != null) {
          query = query.priorityEqualTo(filter.priority!);
        }

        if (filter.cattleId != null && filter.cattleId!.isNotEmpty) {
          query = query.cattleIdEqualTo(filter.cattleId!);
        }

        if (filter.fromDate != null) {
          query = query.createdAtGreaterThan(filter.fromDate!);
        }

        if (filter.toDate != null) {
          query = query.createdAtLessThan(filter.toDate!);
        }

        // Text search (simple implementation)
        if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
          final searchTerm = filter.searchQuery!.toLowerCase();
          query = query.titleContains(searchTerm, caseSensitive: false);
        }
      }

      // Get all notifications from the collection
      // Note: The notification filter system is not fully implemented yet,
      // so we'll get all notifications and filter in memory
      final allNotifications = await _isarService.notificationIsars.where().findAll();

      // Apply pagination in memory
      List<NotificationIsar> notifications;
      if (offset != null || limit != null) {
        final startIndex = offset ?? 0;
        final endIndex = limit != null ? startIndex + limit : allNotifications.length;
        notifications = allNotifications.sublist(
          startIndex.clamp(0, allNotifications.length),
          endIndex.clamp(0, allNotifications.length),
        );
      } else {
        notifications = allNotifications;
      }
      
      // Sorting is now handled by the query

      // Process after loading (deserialize JSON fields)
      for (final notification in notifications) {
        notification.processAfterLoad();
      }

      return notifications;
    } catch (e) {
      _logger.severe('Error getting notifications: $e');
      rethrow;
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String businessId) async {
    try {
      await _isarService.isar.writeTxn(() async {
        final notification = await _isarService.notificationIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (notification != null) {
          notification.status = NotificationStatus.read;
          notification.readAt = DateTime.now();
          notification.updatedAt = DateTime.now();
          await _isarService.notificationIsars.put(notification);
        }
      });

      _logger.info('Marked notification as read: $businessId');
    } catch (e) {
      _logger.severe('Error marking notification as read: $e');
      rethrow;
    }
  }

  /// Delete a notification by its business ID
  Future<void> deleteNotification(String businessId) async {
    try {
      await _isarService.isar.writeTxn(() async {
        final notification = await _isarService.notificationIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (notification != null) {
          await _isarService.notificationIsars.delete(notification.id);
        }
      });

      _logger.info('Deleted notification: $businessId');
    } catch (e) {
      _logger.severe('Error deleting notification: $e');
      rethrow;
    }
  }

  /// Mark multiple notifications as read
  Future<void> markMultipleAsRead(List<String> businessIds) async {
    try {
      await _isarService.isar.writeTxn(() async {
        for (final businessId in businessIds) {
          final notification = await _isarService.notificationIsars
              .filter()
              .businessIdEqualTo(businessId)
              .findFirst();

          if (notification != null) {
            notification.status = NotificationStatus.read;
            notification.readAt = DateTime.now();
            notification.updatedAt = DateTime.now();
            await _isarService.notificationIsars.put(notification);
          }
        }
      });

      _logger.info('Marked ${businessIds.length} notifications as read');
    } catch (e) {
      _logger.severe('Error marking multiple notifications as read: $e');
      rethrow;
    }
  }

  /// Delete multiple notifications by their business IDs
  Future<void> deleteMultiple(List<String> businessIds) async {
    try {
      await _isarService.isar.writeTxn(() async {
        for (final businessId in businessIds) {
          final notification = await _isarService.notificationIsars
              .filter()
              .businessIdEqualTo(businessId)
              .findFirst();

          if (notification != null) {
            await _isarService.notificationIsars.delete(notification.id);
          }
        }
      });

      _logger.info('Deleted ${businessIds.length} notifications');
    } catch (e) {
      _logger.severe('Error deleting multiple notifications: $e');
      rethrow;
    }
  }

  /// Mark all notifications as read, optionally filtered by category
  Future<void> markAllAsRead({String? category}) async {
    try {
      await _isarService.isar.writeTxn(() async {
        var query = _isarService.notificationIsars
            .filter()
            .statusEqualTo(NotificationStatus.unread);

        if (category != null && category.isNotEmpty) {
          query = query.and().categoryEqualTo(category);
        }

        final notifications = await query.findAll();
        final now = DateTime.now();

        for (final notification in notifications) {
          notification.status = NotificationStatus.read;
          notification.readAt = now;
          notification.updatedAt = now;
        }

        await _isarService.notificationIsars.putAll(notifications);
      });

      _logger.info('Marked all notifications as read${category != null ? ' for category: $category' : ''}');
    } catch (e) {
      _logger.severe('Error marking all notifications as read: $e');
      rethrow;
    }
  }

  /// Get the count of unread notifications, optionally filtered by category
  Future<int> getUnreadCount({String? category}) async {
    try {
      var query = _isarService.notificationIsars
          .filter()
          .statusEqualTo(NotificationStatus.unread);

      if (category != null && category.isNotEmpty) {
        query = query.and().categoryEqualTo(category);
      }

      return await query.count();
    } catch (e) {
      _logger.severe('Error getting unread notification count: $e');
      rethrow;
    }
  }

  /// Get notification counts by category
  Future<Map<String, int>> getNotificationCountsByCategory() async {
    try {
      final result = <String, int>{};
      final notifications = await _isarService.notificationIsars.where().findAll();

      // Group by category and count
      for (final notification in notifications) {
        final category = notification.category ?? 'uncategorized';
        result[category] = (result[category] ?? 0) + 1;
      }

      return result;
    } catch (e) {
      _logger.severe('Error getting notification counts by category: $e');
      rethrow;
    }
  }

  /// Get overdue notifications (scheduled but not actioned)
  Future<List<NotificationIsar>> getOverdueNotifications() async {
    try {
      final now = DateTime.now();
      final notifications = await _isarService.notificationIsars
          .filter()
          .scheduledForLessThan(now)
          .and()
          .statusEqualTo(NotificationStatus.unread)
          .findAll();

      // Process after loading (deserialize JSON fields)
      for (final notification in notifications) {
        notification.processAfterLoad();
      }

      return notifications;
    } catch (e) {
      _logger.severe('Error getting overdue notifications: $e');
      rethrow;
    }
  }

  /// Archive a notification
  Future<void> archiveNotification(String businessId) async {
    try {
      await _isarService.isar.writeTxn(() async {
        final notification = await _isarService.notificationIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (notification != null) {
          notification.status = NotificationStatus.archived;
          notification.updatedAt = DateTime.now();
          await _isarService.notificationIsars.put(notification);
        }
      });

      _logger.info('Archived notification: $businessId');
    } catch (e) {
      _logger.severe('Error archiving notification: $e');
      rethrow;
    }
  }

  /// Mark a notification as actioned
  Future<void> markAsActioned(String businessId) async {
    try {
      await _isarService.isar.writeTxn(() async {
        final notification = await _isarService.notificationIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (notification != null) {
          notification.status = NotificationStatus.actioned;
          notification.actionedAt = DateTime.now();
          notification.updatedAt = DateTime.now();
          await _isarService.notificationIsars.put(notification);
        }
      });

      _logger.info('Marked notification as actioned: $businessId');
    } catch (e) {
      _logger.severe('Error marking notification as actioned: $e');
      rethrow;
    }
  }

  /// Clean up old notifications based on retention settings
  Future<int> cleanupOldNotifications(int maxDaysToKeep, int maxReadDaysToKeep) async {
    try {
      final now = DateTime.now();
      final oldestDate = now.subtract(Duration(days: maxDaysToKeep));
      final oldestReadDate = now.subtract(Duration(days: maxReadDaysToKeep));

      int deletedCount = 0;

      await _isarService.isar.writeTxn(() async {
        // Delete old notifications regardless of status
        final oldNotifications = await _isarService.notificationIsars
            .filter()
            .createdAtLessThan(oldestDate)
            .findAll();

        // Delete read notifications that are older than maxReadDaysToKeep
        final oldReadNotifications = await _isarService.notificationIsars
            .filter()
            .group((q) => q
                .statusEqualTo(NotificationStatus.read)
                .or()
                .statusEqualTo(NotificationStatus.actioned)
                .or()
                .statusEqualTo(NotificationStatus.archived))
            .and()
            .createdAtLessThan(oldestReadDate)
            .findAll();

        // Combine the lists and remove duplicates
        final toDelete = <NotificationIsar>{...oldNotifications, ...oldReadNotifications}.toList();
        
        // Delete all at once
        for (final notification in toDelete) {
          await _isarService.notificationIsars.delete(notification.id);
        }

        deletedCount = toDelete.length;
      });

      _logger.info('Cleaned up $deletedCount old notifications');
      return deletedCount;
    } catch (e) {
      _logger.severe('Error cleaning up old notifications: $e');
      rethrow;
    }
  }
}