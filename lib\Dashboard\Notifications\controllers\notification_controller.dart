import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:logging/logging.dart';

import '../services/notification_service.dart';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_request.dart';
import '../models/notification_status.dart';

/// Controller for managing notification UI state
class NotificationController extends ChangeNotifier {
  final Logger _logger = Logger('NotificationController');
  final NotificationService _service = GetIt.instance<NotificationService>();

  // State variables
  List<NotificationIsar> _notifications = [];
  bool _isLoading = false;
  String? _error;
  NotificationFilter? _currentFilter;
  int _unreadCount = 0;
  Map<String, int> _categoryCounts = {};

  // Getters
  List<NotificationIsar> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get error => _error;
  NotificationFilter? get currentFilter => _currentFilter;
  int get unreadCount => _unreadCount;
  Map<String, int> get categoryCounts => _categoryCounts;

  /// Initialize the controller
  Future<void> initialize() async {
    try {
      _setLoading(true);
      await loadNotifications();
      await loadUnreadCount();
      await loadCategoryCounts();
      
      // Listen to notification changes
      _service.onNotificationChanges.listen((_) {
        refreshData();
      });
      
      _logger.info('NotificationController initialized');
    } catch (e) {
      _setError('Failed to initialize notifications: $e');
      _logger.severe('Error initializing NotificationController: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load notifications with current filter
  Future<void> loadNotifications() async {
    try {
      _setLoading(true);
      _clearError();
      
      final notifications = await _service.getNotifications(filter: _currentFilter);
      _notifications = notifications;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load notifications: $e');
      _logger.severe('Error loading notifications: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load unread count
  Future<void> loadUnreadCount() async {
    try {
      final count = await _service.getUnreadCount();
      _unreadCount = count;
      notifyListeners();
    } catch (e) {
      _logger.severe('Error loading unread count: $e');
    }
  }

  /// Load category counts
  Future<void> loadCategoryCounts() async {
    try {
      final counts = await _service.getNotificationCountsByCategory();
      _categoryCounts = counts;
      notifyListeners();
    } catch (e) {
      _logger.severe('Error loading category counts: $e');
    }
  }

  /// Apply filter to notifications
  Future<void> applyFilter(NotificationFilter filter) async {
    _currentFilter = filter;
    await loadNotifications();
  }

  /// Clear current filter
  Future<void> clearFilter() async {
    _currentFilter = null;
    await loadNotifications();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _service.markAsRead(notificationId);
      
      // Update local state
      final index = _notifications.indexWhere((n) => n.businessId == notificationId);
      if (index != -1) {
        _notifications[index].status = NotificationStatus.read;
        _notifications[index].readAt = DateTime.now();
        notifyListeners();
      }
      
      await loadUnreadCount();
    } catch (e) {
      _setError('Failed to mark notification as read: $e');
      _logger.severe('Error marking notification as read: $e');
    }
  }

  /// Mark notification as actioned
  Future<void> markAsActioned(String notificationId) async {
    try {
      await _service.markAsActioned(notificationId);
      
      // Update local state
      final index = _notifications.indexWhere((n) => n.businessId == notificationId);
      if (index != -1) {
        _notifications[index].status = NotificationStatus.actioned;
        _notifications[index].actionedAt = DateTime.now();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to mark notification as actioned: $e');
      _logger.severe('Error marking notification as actioned: $e');
    }
  }

  /// Archive notification
  Future<void> archiveNotification(String notificationId) async {
    try {
      await _service.archiveNotification(notificationId);
      
      // Remove from local state
      _notifications.removeWhere((n) => n.businessId == notificationId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to archive notification: $e');
      _logger.severe('Error archiving notification: $e');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _service.deleteNotification(notificationId);
      
      // Remove from local state
      _notifications.removeWhere((n) => n.businessId == notificationId);
      notifyListeners();
      
      await loadUnreadCount();
    } catch (e) {
      _setError('Failed to delete notification: $e');
      _logger.severe('Error deleting notification: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead({String? category}) async {
    try {
      await _service.markAllAsRead(category: category);
      
      // Update local state
      for (var notification in _notifications) {
        if (category == null || notification.category == category) {
          notification.status = NotificationStatus.read;
          notification.readAt = DateTime.now();
        }
      }
      
      notifyListeners();
      await loadUnreadCount();
    } catch (e) {
      _setError('Failed to mark all as read: $e');
      _logger.severe('Error marking all as read: $e');
    }
  }

  /// Mark multiple notifications as read
  Future<void> markMultipleAsRead(List<String> notificationIds) async {
    try {
      await _service.markMultipleAsRead(notificationIds);
      
      // Update local state
      for (var id in notificationIds) {
        final index = _notifications.indexWhere((n) => n.businessId == id);
        if (index != -1) {
          _notifications[index].status = NotificationStatus.read;
          _notifications[index].readAt = DateTime.now();
        }
      }
      
      notifyListeners();
      await loadUnreadCount();
    } catch (e) {
      _setError('Failed to mark multiple as read: $e');
      _logger.severe('Error marking multiple as read: $e');
    }
  }

  /// Delete multiple notifications
  Future<void> deleteMultiple(List<String> notificationIds) async {
    try {
      await _service.deleteMultiple(notificationIds);
      
      // Remove from local state
      _notifications.removeWhere((n) => notificationIds.contains(n.businessId));
      notifyListeners();
      
      await loadUnreadCount();
    } catch (e) {
      _setError('Failed to delete multiple notifications: $e');
      _logger.severe('Error deleting multiple notifications: $e');
    }
  }

  /// Create a new notification
  Future<void> createNotification(NotificationRequest request) async {
    try {
      final notification = await _service.createNotification(request);
      
      // Add to local state
      _notifications.insert(0, notification);
      notifyListeners();
      
      await loadUnreadCount();
    } catch (e) {
      _setError('Failed to create notification: $e');
      _logger.severe('Error creating notification: $e');
    }
  }

  /// Refresh all data
  Future<void> refreshData() async {
    await Future.wait([
      loadNotifications(),
      loadUnreadCount(),
      loadCategoryCounts(),
    ]);
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error state
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error state
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
    _logger.info('NotificationController disposed');
  }
}