import 'package:logging/logging.dart';
import 'package:isar/isar.dart';

import '../models/notification_isar.dart';
import '../models/notification_settings_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Consolidated repository for all Notifications module database operations
class NotificationsRepository {
  static final Logger _logger = Logger('NotificationsRepository');
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  NotificationsRepository(this._isarService);
  
  // Getter for Isar instance
  Isar get _isar => _isarService.isar;
  
  //=== NOTIFICATIONS ===//
  
  /// Get all notifications
  Future<List<NotificationIsar>> getAllNotifications() async {
    try {
      return await _isar.notificationIsars
          .where()
          .sortByCreatedAtDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting all notifications: $e');
      throw DatabaseException('Failed to retrieve notifications', e.toString());
    }
  }

  /// Get unread notifications
  Future<List<NotificationIsar>> getUnreadNotifications() async {
    try {
      return await _isar.notificationIsars
          .filter()
          .isReadEqualTo(false)
          .sortByCreatedAtDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting unread notifications: $e');
      throw DatabaseException('Failed to retrieve unread notifications', e.toString());
    }
  }

  /// Get notifications by type
  Future<List<NotificationIsar>> getNotificationsByType(String type) async {
    try {
      if (type.isEmpty) {
        throw ValidationException('Notification type is required');
      }

      return await _isar.notificationIsars
          .filter()
          .typeEqualTo(type)
          .sortByCreatedAtDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting notifications by type $type: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve notifications', e.toString());
    }
  }

  /// Add a new notification
  Future<void> addNotification(NotificationIsar notification) async {
    try {
      await _validateNotification(notification, isNew: true);

      await _isar.writeTxn(() async {
        await _isar.notificationIsars.put(notification);
      });

      _logger.info('Successfully added notification: $notification.businessId');
    } catch (e) {
      _logger.severe('Error adding notification: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add notification', e.toString());
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Notification ID is required');
      }

      await _isar.writeTxn(() async {
        final notification = await _isar.notificationIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (notification == null) {
          throw RecordNotFoundException('Notification not found: $businessId');
        }

        notification.isRead = true;
        notification.readAt = DateTime.now();
        await _isar.notificationIsars.put(notification);
      });

      _logger.info('Successfully marked notification as read: $businessId');
    } catch (e) {
      _logger.severe('Error marking notification as read: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to mark notification as read', e.toString());
    }
  }

  /// Mark all notifications as read
  Future<void> markAllNotificationsAsRead() async {
    try {
      await _isar.writeTxn(() async {
        final unreadNotifications = await _isar.notificationIsars
            .filter()
            .isReadEqualTo(false)
            .findAll();

        for (final notification in unreadNotifications) {
          notification.isRead = true;
          notification.readAt = DateTime.now();
        }

        await _isar.notificationIsars.putAll(unreadNotifications);
      });

      _logger.info('Successfully marked all notifications as read');
    } catch (e) {
      _logger.severe('Error marking all notifications as read: $e');
      throw DatabaseException('Failed to mark all notifications as read', e.toString());
    }
  }

  /// Delete a notification
  Future<void> deleteNotification(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Notification ID is required');
      }

      await _isar.writeTxn(() async {
        final notification = await _isar.notificationIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (notification == null) {
          throw RecordNotFoundException('Notification not found: $businessId');
        }

        await _isar.notificationIsars.delete(notification.id);
      });

      _logger.info('Successfully deleted notification: $businessId');
    } catch (e) {
      _logger.severe('Error deleting notification: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete notification', e.toString());
    }
  }

  /// Delete all read notifications
  Future<void> deleteAllReadNotifications() async {
    try {
      await _isar.writeTxn(() async {
        final readNotifications = await _isar.notificationIsars
            .filter()
            .isReadEqualTo(true)
            .findAll();

        await _isar.notificationIsars.deleteAll(
          readNotifications.map((n) => n.id).toList(),
        );
      });

      _logger.info('Successfully deleted all read notifications');
    } catch (e) {
      _logger.severe('Error deleting read notifications: $e');
      throw DatabaseException('Failed to delete read notifications', e.toString());
    }
  }

  //=== NOTIFICATION SETTINGS ===//

  /// Get notification settings
  Future<NotificationSettingsIsar?> getNotificationSettings() async {
    try {
      return await _isar.notificationSettingsIsars
          .where()
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting notification settings: $e');
      throw DatabaseException('Failed to retrieve notification settings', e.toString());
    }
  }

  /// Save notification settings
  Future<void> saveNotificationSettings(NotificationSettingsIsar settings) async {
    try {
      _validateNotificationSettings(settings);

      await _isar.writeTxn(() async {
        await _isar.notificationSettingsIsars.put(settings);
      });

      _logger.info('Successfully saved notification settings');
    } catch (e) {
      _logger.severe('Error saving notification settings: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save notification settings', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate notification
  Future<void> _validateNotification(NotificationIsar notification, {required bool isNew}) async {
    if (notification.title == null || notification.title!.isEmpty) {
      throw ValidationException('Notification title is required');
    }

    if (notification.message == null || notification.message!.isEmpty) {
      throw ValidationException('Notification message is required');
    }

    if (notification.type == null || notification.type!.isEmpty) {
      throw ValidationException('Notification type is required');
    }

    // Set default values for new records
    if (isNew) {
      notification.createdAt = DateTime.now();
      notification.isRead = false;
      notification.readAt = null;
    }
  }

  /// Validate notification settings
  void _validateNotificationSettings(NotificationSettingsIsar settings) {
    // All these properties are non-nullable in our model, so we only need to check
    // that the settings object itself is not null, which is handled by the parameter type.
    // Instead of null checks, we can add any additional validation requirements here.
  }
} 