import 'package:isar/isar.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../models/event_enums.dart';
import '../../../services/database/isar_service.dart';
import 'event_performance_service.dart';

/// Pure reactive repository for Events module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class EventsRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  EventsRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE EVENT STREAMS ===//

  /// Watches all events with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<EventIsar>> watchAllEvents() {
    return _isar.eventIsars.where().watch(fireImmediately: true);
  }

  /// Watches all event types with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<EventTypeIsar>> watchAllEventTypes() {
    return _isar.eventTypeIsars.where().watch(fireImmediately: true);
  }

  /// Watches event attachments for a specific event with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<EventAttachmentIsar>> watchEventAttachments(String eventBusinessId) {
    return _isar.eventAttachmentIsars
        .filter()
        .eventBusinessIdEqualTo(eventBusinessId)
        .watch(fireImmediately: true);
  }

  //=== EVENT CRUD ===//

  /// Save (add or update) an event record using Isar's native upsert
  Future<void> saveEvent(EventIsar event) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars.put(event);
    });
  }

  /// Delete an event record by its Isar ID
  Future<void> deleteEvent(int eventId) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars.delete(eventId);
    });
  }

  /// Delete an event record by its business ID
  Future<void> deleteEventByBusinessId(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== EVENT TYPE CRUD ===//

  /// Save (add or update) an event type record using Isar's native upsert
  Future<void> saveEventType(EventTypeIsar eventType) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars.put(eventType);
    });
  }

  /// Delete an event type record by its Isar ID
  Future<void> deleteEventType(int eventTypeId) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars.delete(eventTypeId);
    });
  }

  /// Delete an event type record by its business ID
  Future<void> deleteEventTypeByBusinessId(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== EVENT ATTACHMENT CRUD ===//

  /// Save (add or update) an event attachment record using Isar's native upsert
  Future<void> saveEventAttachment(EventAttachmentIsar attachment) async {
    await _isar.writeTxn(() async {
      await _isar.eventAttachmentIsars.put(attachment);
    });
  }

  /// Delete an event attachment record by its Isar ID
  Future<void> deleteEventAttachment(int attachmentId) async {
    await _isar.writeTxn(() async {
      await _isar.eventAttachmentIsars.delete(attachmentId);
    });
  }

  /// Delete an event attachment record by its business ID
  Future<void> deleteEventAttachmentByBusinessId(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.eventAttachmentIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  /// Delete all attachments for a specific event
  Future<void> deleteEventAttachmentsByEventId(String eventBusinessId) async {
    await _isar.writeTxn(() async {
      await _isar.eventAttachmentIsars
          .filter()
          .eventBusinessIdEqualTo(eventBusinessId)
          .deleteAll();
    });
  }

  //=== QUERY METHODS FOR ANALYTICS AND VALIDATION ===//

  /// Get all events (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getAllEvents() async {
    return await _isar.eventIsars.where().findAll();
  }

  /// Get all event types (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventTypeIsar>> getAllEventTypes() async {
    return await _isar.eventTypeIsars.where().findAll();
  }

  /// Get all event attachments (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventAttachmentIsar>> getAllEventAttachments() async {
    return await _isar.eventAttachmentIsars.where().findAll();
  }

  /// Get events by date range (for analytics and reporting)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getEventsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.eventIsars
        .filter()
        .scheduledDateBetween(startDate, endDate)
        .findAll();
  }

  /// Get events by cattle tag ID (for cattle-specific analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getEventsByCattle(String cattleTagId) async {
    return await _isar.eventIsars
        .filter()
        .cattleTagIdEqualTo(cattleTagId, caseSensitive: false)
        .findAll();
  }

  /// Get events by category (for category-specific analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getEventsByCategory(EventCategory category) async {
    return await _isar.eventIsars
        .filter()
        .categoryEqualTo(category)
        .findAll();
  }

  /// Get events by status (for status-specific analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getEventsByStatus(EventStatus status) async {
    return await _isar.eventIsars
        .filter()
        .statusEqualTo(status)
        .findAll();
  }

  /// Get overdue events (for notifications and alerts)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getOverdueEvents() async {
    final now = DateTime.now();
    return await _isar.eventIsars
        .filter()
        .scheduledDateLessThan(now)
        .and()
        .statusEqualTo(EventStatus.scheduled)
        .or()
        .statusEqualTo(EventStatus.inProgress)
        .findAll();
  }

  /// Get upcoming events within specified days (for notifications and planning)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getUpcomingEvents(int days) async {
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: days));
    return await _isar.eventIsars
        .filter()
        .scheduledDateBetween(now, futureDate)
        .and()
        .statusEqualTo(EventStatus.scheduled)
        .or()
        .statusEqualTo(EventStatus.inProgress)
        .findAll();
  }

  /// Get events for a specific date (for calendar views)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getEventsForDate(DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);
    return await _isar.eventIsars
        .filter()
        .scheduledDateBetween(startOfDay, endOfDay)
        .findAll();
  }

  /// Get event by business ID (for validation and navigation)
  /// Returns a Future<EventIsar?> for one-time data fetching
  Future<EventIsar?> getEventByBusinessId(String businessId) async {
    return await _isar.eventIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get event type by business ID (for validation and navigation)
  /// Returns a Future<EventTypeIsar?> for one-time data fetching
  Future<EventTypeIsar?> getEventTypeByBusinessId(String businessId) async {
    return await _isar.eventTypeIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get event attachment by business ID (for validation and navigation)
  /// Returns a Future<EventAttachmentIsar?> for one-time data fetching
  Future<EventAttachmentIsar?> getEventAttachmentByBusinessId(String businessId) async {
    return await _isar.eventAttachmentIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get attachments for a specific event (for event details)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventAttachmentIsar>> getAttachmentsForEvent(String eventBusinessId) async {
    return await _isar.eventAttachmentIsars
        .filter()
        .eventBusinessIdEqualTo(eventBusinessId)
        .findAll();
  }

  /// Get events by event type (for type-specific analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getEventsByEventType(String eventTypeBusinessId) async {
    return await _isar.eventIsars
        .filter()
        .eventTypeIdEqualTo(eventTypeBusinessId)
        .findAll();
  }

  /// Get recurring events by parent event ID (for recurrence management)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getRecurringEventsByParentId(String parentEventId) async {
    return await _isar.eventIsars
        .filter()
        .parentEventIdEqualTo(parentEventId)
        .findAll();
  }

  /// Get auto-generated events by source module (for automation analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getAutoGeneratedEventsBySource(String sourceModule) async {
    return await _isar.eventIsars
        .filter()
        .isAutoGeneratedEqualTo(true)
        .and()
        .sourceModuleEqualTo(sourceModule)
        .findAll();
  }

  /// Update event - alias for saveEvent for backward compatibility
  Future<void> updateEvent(EventIsar event) async {
    await saveEvent(event);
  }

  /// Update event type - alias for saveEventType for backward compatibility
  Future<void> updateEventType(EventTypeIsar eventType) async {
    await saveEventType(eventType);
  }

  /// Update event attachment - alias for saveEventAttachment for backward compatibility
  Future<void> updateEventAttachment(EventAttachmentIsar attachment) async {
    await saveEventAttachment(attachment);
  }

  //=== SEARCH AND FILTER OPTIMIZATION METHODS ===//

  /// Search events by text across multiple fields with database-level optimization
  /// Uses indexed fields for optimal performance
  Future<List<EventIsar>> searchEvents(String searchTerm) async {
    if (searchTerm.trim().isEmpty) {
      return await getAllEvents();
    }

    final term = searchTerm.toLowerCase().trim();
    
    return await _isar.eventIsars
        .filter()
        .group((q) => q
            .titleContains(term, caseSensitive: false)
            .or()
            .descriptionContains(term, caseSensitive: false)
            .or()
            .notesContains(term, caseSensitive: false)
            .or()
            .cattleTagIdContains(term, caseSensitive: false)
            .or()
            .locationContains(term, caseSensitive: false)
            .or()
            .completionNotesContains(term, caseSensitive: false)
            .or()
            .completedByContains(term, caseSensitive: false))
        .sortByScheduledDateDesc()
        .findAll();
  }

  /// Get paginated events with performance optimization
  Future<PaginatedResult<EventIsar>> getPaginatedEvents({
    int page = 0,
    int pageSize = 50,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    EventCategory? category,
    EventStatus? status,
    EventPriority? priority,
    String? cattleTagId,
    String? eventTypeId,
    bool? isAutoGenerated,
    bool? isRecurring,
    String? location,
    String? completedBy,
  }) async {
    // Build optimized query with pagination
    final query = _buildOptimizedQuery(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      category: category,
      status: status,
      priority: priority,
      cattleTagId: cattleTagId,
      eventTypeId: eventTypeId,
      isAutoGenerated: isAutoGenerated,
      isRecurring: isRecurring,
      location: location,
      completedBy: completedBy,
    );

    // Get total count for pagination
    final totalCount = await query.count();
    
    // Get paginated results
    final results = await query
        .offset(page * pageSize)
        .limit(pageSize)
        .findAll();

    return PaginatedResult<EventIsar>(
      items: results,
      currentPage: page,
      totalPages: (totalCount / pageSize).ceil(),
      totalItems: totalCount,
      pageSize: pageSize,
      hasNextPage: (page + 1) * pageSize < totalCount,
      hasPreviousPage: page > 0,
    );
  }

  /// Get events with advanced filtering using database-level queries
  /// Optimized for performance with proper indexing
  Future<List<EventIsar>> getEventsWithFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    EventCategory? category,
    EventStatus? status,
    EventPriority? priority,
    String? cattleTagId,
    String? eventTypeId,
    bool? isAutoGenerated,
    bool? isRecurring,
    String? location,
    String? completedBy,
  }) async {
    final query = _buildOptimizedQuery(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      category: category,
      status: status,
      priority: priority,
      cattleTagId: cattleTagId,
      eventTypeId: eventTypeId,
      isAutoGenerated: isAutoGenerated,
      isRecurring: isRecurring,
      location: location,
      completedBy: completedBy,
    );

    return await query.findAll();
  }

  /// Build optimized query with proper indexing
  dynamic _buildOptimizedQuery({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    EventCategory? category,
    EventStatus? status,
    EventPriority? priority,
    String? cattleTagId,
    String? eventTypeId,
    bool? isAutoGenerated,
    bool? isRecurring,
    String? location,
    String? completedBy,
  }) {
    // Start with optimized base query using indexed fields
    dynamic currentQuery;
    
    // Use most selective filter first for optimal performance
    if (cattleTagId?.isNotEmpty ?? false) {
      // Use cattle index for cattle-specific queries
      currentQuery = _isar.eventIsars.filter().cattleTagIdEqualTo(cattleTagId!);
    } else if (category != null) {
      // Use category index for category-specific queries
      currentQuery = _isar.eventIsars.filter().categoryEqualTo(category);
    } else if (status != null) {
      // Use status index for status-specific queries
      currentQuery = _isar.eventIsars.filter().statusEqualTo(status);
    } else if (startDate != null || endDate != null) {
      // Use date index for date range queries
      if (startDate != null && endDate != null) {
        currentQuery = _isar.eventIsars.filter()
            .scheduledDateBetween(startDate, endDate.add(const Duration(days: 1)));
      } else if (startDate != null) {
        currentQuery = _isar.eventIsars.filter().scheduledDateGreaterThan(startDate);
      } else {
        final inclusiveEndDate = endDate!.add(const Duration(days: 1));
        currentQuery = _isar.eventIsars.filter().scheduledDateLessThan(inclusiveEndDate);
      }
    } else {
      // Default to all events
      currentQuery = _isar.eventIsars.filter();
    }

    // Apply additional filters
    if (searchQuery?.isNotEmpty ?? false) {
      final searchTerm = searchQuery!.toLowerCase().trim();
      currentQuery = currentQuery.and().group((q) => q
          .titleContains(searchTerm, caseSensitive: false)
          .or()
          .descriptionContains(searchTerm, caseSensitive: false)
          .or()
          .notesContains(searchTerm, caseSensitive: false)
          .or()
          .locationContains(searchTerm, caseSensitive: false)
          .or()
          .completionNotesContains(searchTerm, caseSensitive: false)
          .or()
          .completedByContains(searchTerm, caseSensitive: false));
    }

    // Apply remaining filters only if not already applied
    if (category != null && cattleTagId?.isEmpty != false) {
      currentQuery = currentQuery.and().categoryEqualTo(category);
    }

    if (status != null && cattleTagId?.isEmpty != false && category == null) {
      currentQuery = currentQuery.and().statusEqualTo(status);
    }

    if (priority != null) {
      currentQuery = currentQuery.and().priorityEqualTo(priority);
    }

    if (eventTypeId?.isNotEmpty ?? false) {
      currentQuery = currentQuery.and().eventTypeIdEqualTo(eventTypeId!);
    }

    if (isAutoGenerated != null) {
      currentQuery = currentQuery.and().isAutoGeneratedEqualTo(isAutoGenerated);
    }

    if (isRecurring != null) {
      currentQuery = currentQuery.and().isRecurringEqualTo(isRecurring);
    }

    if (location?.isNotEmpty ?? false) {
      currentQuery = currentQuery.and().locationContains(location!, caseSensitive: false);
    }

    if (completedBy?.isNotEmpty ?? false) {
      currentQuery = currentQuery.and().completedByContains(completedBy!, caseSensitive: false);
    }

    // Apply optimal sorting using indexed field
    return currentQuery.sortByScheduledDateDesc();
  }

  /// Get events by multiple categories (for advanced filtering)
  Future<List<EventIsar>> getEventsByCategories(List<EventCategory> categories) async {
    if (categories.isEmpty) return [];
    
    return await _isar.eventIsars
        .filter()
        .anyOf(categories, (q, category) => q.categoryEqualTo(category))
        .sortByScheduledDateDesc()
        .findAll();
  }

  /// Get events by multiple statuses (for advanced filtering)
  Future<List<EventIsar>> getEventsByStatuses(List<EventStatus> statuses) async {
    if (statuses.isEmpty) return [];
    
    return await _isar.eventIsars
        .filter()
        .anyOf(statuses, (q, status) => q.statusEqualTo(status))
        .sortByScheduledDateDesc()
        .findAll();
  }

  /// Get events by text in specific field (optimized for specific searches)
  Future<List<EventIsar>> getEventsByField(String fieldName, String searchTerm) async {
    if (searchTerm.trim().isEmpty) return [];
    
    final term = searchTerm.toLowerCase().trim();
    
    switch (fieldName.toLowerCase()) {
      case 'title':
        return await _isar.eventIsars
            .filter()
            .titleContains(term, caseSensitive: false)
            .sortByScheduledDateDesc()
            .findAll();
      case 'description':
        return await _isar.eventIsars
            .filter()
            .descriptionContains(term, caseSensitive: false)
            .sortByScheduledDateDesc()
            .findAll();
      case 'notes':
        return await _isar.eventIsars
            .filter()
            .notesContains(term, caseSensitive: false)
            .sortByScheduledDateDesc()
            .findAll();
      case 'location':
        return await _isar.eventIsars
            .filter()
            .locationContains(term, caseSensitive: false)
            .sortByScheduledDateDesc()
            .findAll();
      case 'completedby':
        return await _isar.eventIsars
            .filter()
            .completedByContains(term, caseSensitive: false)
            .sortByScheduledDateDesc()
            .findAll();
      default:
        return await searchEvents(searchTerm);
    }
  }
}