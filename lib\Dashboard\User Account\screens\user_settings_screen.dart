import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/user_profile_controller.dart';
import '../controllers/auth_controller.dart';
import '../services/security_service.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_bar.dart';
import 'email_verification_screen.dart';
import '../../Farm Setup/screens/data_backup_screen.dart';
import '../../Farm Setup/screens/alerts_settings_screen.dart';

/// Screen for managing user account settings
class UserSettingsScreen extends StatefulWidget {
  const UserSettingsScreen({super.key});

  @override
  State<UserSettingsScreen> createState() => _UserSettingsScreenState();
}

class _UserSettingsScreenState extends State<UserSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => UserProfileController()..initialize()),
        ChangeNotifierProvider(create: (context) => AuthController()..initialize()),
      ],
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBarConfig.withBack(
          title: 'Account Settings',
          context: context,
        ),
        body: Consumer2<UserProfileController, AuthController>(
          builder: (context, profileController, authController, child) {
            if (!authController.isAuthenticated) {
              return const Center(
                child: Text('Please sign in to access settings'),
              );
            }

            if (profileController.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Account Section
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Account',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),

                          _buildSettingsTile(
                            icon: Icons.email_outlined,
                            title: 'Email Verification',
                            subtitle: authController.currentUser?.isEmailVerified == true
                                ? 'Email verified'
                                : 'Verify your email address',
                            trailing: authController.currentUser?.isEmailVerified == true
                                ? Icon(Icons.check_circle, color: Colors.green[600])
                                : Icon(Icons.warning_amber, color: Colors.orange[600]),
                            onTap: () {
                              if (authController.currentUser?.isEmailVerified != true) {
                                _showEmailVerificationDialog(context);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Preferences Section
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Preferences',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildSettingsTile(
                            icon: Icons.notifications_outlined,
                            title: 'Notifications',
                            subtitle: 'Manage notification preferences',
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const AlertsSettingsScreen(),
                                ),
                              );
                            },
                          ),
                          _buildSettingsTile(
                            icon: Icons.palette_outlined,
                            title: 'Theme',
                            subtitle: 'Choose app appearance',
                            onTap: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Theme settings coming soon')),
                              );
                            },
                          ),
                          _buildSettingsTile(
                            icon: Icons.language_outlined,
                            title: 'Language',
                            subtitle: 'English',
                            onTap: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Language settings coming soon')),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Data & Privacy Section
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Data & Privacy',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildSettingsTile(
                            icon: Icons.backup_outlined,
                            title: 'Data Backup',
                            subtitle: 'Backup your cattle data',
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const DataBackupScreen(),
                                ),
                              );
                            },
                          ),
                          _buildSettingsTile(
                            icon: Icons.privacy_tip_outlined,
                            title: 'Privacy Settings',
                            subtitle: 'Manage data privacy and sharing',
                            onTap: () {
                              _showPrivacySettingsDialog(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Security Section
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Security',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildSettingsTile(
                            icon: Icons.lock_outline,
                            title: 'Change Password',
                            subtitle: 'Update your account password',
                            onTap: () {
                              _showChangePasswordDialog(context, authController);
                            },
                          ),
                          // Biometric Authentication Setting
                          FutureBuilder<bool>(
                            future: authController.isBiometricAvailable(),
                            builder: (context, snapshot) {
                              final isAvailable = snapshot.data ?? false;

                              if (isAvailable) {
                                // Device supports biometric authentication
                                return FutureBuilder<bool>(
                                  future: authController.isBiometricEnabled(),
                                  builder: (context, enabledSnapshot) {
                                    final isEnabled = enabledSnapshot.data ?? false;
                                    return FutureBuilder<String>(
                                      future: authController.getBiometricDescription(),
                                      builder: (context, descSnapshot) {
                                        return _buildBiometricTile(
                                          authController: authController,
                                          isEnabled: isEnabled,
                                          description: descSnapshot.data ?? 'Biometric',
                                          isAvailable: true,
                                        );
                                      },
                                    );
                                  },
                                );
                              } else {
                                // Device doesn't support biometric authentication
                                return _buildBiometricTile(
                                  authController: authController,
                                  isEnabled: false,
                                  description: 'Biometric',
                                  isAvailable: false,
                                );
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Danger Zone Section
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Danger Zone',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildSettingsTile(
                            icon: Icons.logout,
                            title: 'Logout',
                            subtitle: 'Sign out of your account',
                            titleColor: Colors.red,
                            onTap: () {
                              _showLogoutDialog(context, authController);
                            },
                          ),
                          _buildSettingsTile(
                            icon: Icons.delete_forever,
                            title: 'Delete Account',
                            subtitle: 'Permanently delete your account',
                            titleColor: Colors.red,
                            onTap: () {
                              _showDeleteAccountDialog(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    Color? titleColor,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (titleColor ?? AppColors.primary).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: titleColor ?? AppColors.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: titleColor,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: trailing ?? const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildBiometricTile({
    required AuthController authController,
    required bool isEnabled,
    required String description,
    bool isAvailable = true,
  }) {
    return _buildSettingsTile(
      icon: isAvailable ? Icons.fingerprint : Icons.fingerprint_outlined,
      title: '$description Authentication',
      subtitle: !isAvailable
          ? 'Not available on this device'
          : isEnabled
              ? 'Enabled - Use $description to sign in'
              : 'Enable $description authentication for quick sign-in',
      trailing: isAvailable
          ? Switch(
              value: isEnabled,
              onChanged: (value) async {
                if (value) {
                  // Enable biometric auth
                  final success = await authController.enableBiometricAuth();
                  if (success) {
                    setState(() {}); // Refresh the UI
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('$description authentication enabled')),
                    );
                  }
                } else {
                  // Disable biometric auth
                  final success = await authController.disableBiometricAuth();
                  if (success) {
                    setState(() {}); // Refresh the UI
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('$description authentication disabled')),
                    );
                  }
                }
              },
            )
          : const Icon(
              Icons.block,
              color: Colors.grey,
            ),
      onTap: null, // Disable tap since we're using the switch
    );
  }

  void _showChangePasswordDialog(BuildContext context, AuthController authController) {
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    final securityService = SecurityService();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          final passwordStrength = newPasswordController.text.isNotEmpty
              ? securityService.analyzePasswordStrength(newPasswordController.text)
              : null;

          return AlertDialog(
            title: const Text('Change Password'),
            content: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: newPasswordController,
                    obscureText: true,
                    onChanged: (value) => setState(() {}),
                    decoration: const InputDecoration(
                      labelText: 'New Password',
                      border: OutlineInputBorder(),
                      helperText: 'Must contain uppercase, lowercase, number, and special character',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a new password';
                      }
                      if (!securityService.isPasswordSecure(value)) {
                        return 'Password does not meet security requirements';
                      }
                      if (securityService.isCommonPassword(value)) {
                        return 'Please choose a less common password';
                      }
                      return null;
                    },
                  ),
                  if (newPasswordController.text.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildPasswordStrengthIndicator(passwordStrength!),
                  ],
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: confirmPasswordController,
                    obscureText: true,
                    decoration: const InputDecoration(
                      labelText: 'Confirm New Password',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value != newPasswordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    authController.changePassword(
                      newPassword: newPasswordController.text,
                    ).then((success) {
                      Navigator.pop(context);
                      if (success) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Password changed successfully')),
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(authController.errorMessage ?? 'Failed to change password')),
                        );
                      }
                    });
                  }
                },
                child: const Text('Change Password'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showPrivacySettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Privacy',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text('• Your cattle data is stored locally on your device'),
            Text('• Backup data is encrypted when stored in the cloud'),
            Text('• We do not share your personal information with third parties'),
            Text('• You can delete your account and all data at any time'),
            SizedBox(height: 16),
            Text(
              'Security Features',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text('• Biometric authentication available in Security section'),
            Text('• Auto-lock functionality to protect your data'),
            Text('• Secure password requirements'),
            Text('• Session management and logout options'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordStrengthIndicator(PasswordStrength strength) {
    Color color;
    String text;
    double progress;

    switch (strength) {
      case PasswordStrength.weak:
        color = Colors.red;
        text = 'Weak';
        progress = 0.33;
        break;
      case PasswordStrength.medium:
        color = Colors.orange;
        text = 'Medium';
        progress = 0.66;
        break;
      case PasswordStrength.strong:
        color = Colors.green;
        text = 'Strong';
        progress = 1.0;
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showEmailVerificationDialog(BuildContext context) {
    final authController = Provider.of<AuthController>(context, listen: false);
    final userEmail = authController.currentUser?.email ?? '';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Email Verification'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Your email address is not verified yet.'),
            const SizedBox(height: 12),
            Text(
              'Email: $userEmail',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            const Text('Would you like to:'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await authController.resendEmailVerification(email: userEmail);
              if (success && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Verification email sent! Please check your inbox.'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('Resend Email'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EmailVerificationScreen(email: userEmail),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[700],
              foregroundColor: Colors.white,
            ),
            child: const Text('Verify Now'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthController authController) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              authController.logout();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text('Account deletion feature will be implemented soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
