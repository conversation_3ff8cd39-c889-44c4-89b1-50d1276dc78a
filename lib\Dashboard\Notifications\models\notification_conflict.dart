import '../models/notification_isar.dart';

/// Represents a conflict between local and server notification data
class NotificationConflict {
  /// The notification ID with the conflict
  final String notificationId;
  
  /// The local version of the notification
  final NotificationIsar localData;
  
  /// The server version of the notification
  final NotificationIsar serverData;
  
  /// Constructor
  NotificationConflict({
    required this.notificationId,
    required this.localData,
    required this.serverData,
  });
  
  /// Convert to a map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'notificationId': notificationId,
      'localData': localData.toMap(),
      'serverData': serverData.toMap(),
    };
  }
}