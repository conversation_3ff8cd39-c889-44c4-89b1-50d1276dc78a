import 'dart:io';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../models/event_enums.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../services/events_repository.dart';
import '../../Cattle/services/cattle_repository.dart';

/// Comprehensive validation service for Events module
/// Provides validation for events, event types, attachments, and recurrence rules
/// Following established validation patterns from cattle module
class EventValidationService {
  final EventsRepository _eventsRepository;
  final CattleRepository _cattleRepository;

  EventValidationService(this._eventsRepository, this._cattleRepository);

  // ===== EVENT VALIDATION =====

  /// Validate event data before saving
  /// Throws ValidationException with user-friendly messages
  Future<void> validateEvent(EventIsar event, {bool isUpdate = false}) async {
    final errors = <String>[];

    // Required field validation
    if (event.title?.trim().isEmpty ?? true) {
      errors.add('Event title is required');
    }

    if (event.scheduledDate == null) {
      errors.add('Scheduled date is required');
    }

    if (event.cattleTagId?.trim().isEmpty ?? true) {
      errors.add('Cattle selection is required');
    }

    if (event.eventTypeId?.trim().isEmpty ?? true) {
      errors.add('Event type is required');
    }

    // Title length validation
    if (event.title != null && event.title!.length > 100) {
      errors.add('Event title cannot exceed 100 characters');
    }

    // Description length validation
    if (event.description != null && event.description!.length > 500) {
      errors.add('Event description cannot exceed 500 characters');
    }

    // Notes length validation
    if (event.notes != null && event.notes!.length > 1000) {
      errors.add('Event notes cannot exceed 1000 characters');
    }

    // Date validation
    if (event.scheduledDate != null) {
      await _validateEventDate(event.scheduledDate!, errors);
    }

    // Cattle existence validation
    if (event.cattleTagId?.isNotEmpty ?? false) {
      await _validateCattleExists(event.cattleTagId!, errors);
    }

    // Event type existence validation
    if (event.eventTypeId?.isNotEmpty ?? false) {
      await _validateEventTypeExists(event.eventTypeId!, errors);
    }

    // Business ID validation for updates
    if (isUpdate && (event.businessId?.trim().isEmpty ?? true)) {
      errors.add('Event ID is required for updates');
    }

    // Cost validation
    if (event.estimatedCost != null && event.estimatedCost! < 0) {
      errors.add('Estimated cost cannot be negative');
    }

    if (event.actualCost != null && event.actualCost! < 0) {
      errors.add('Actual cost cannot be negative');
    }

    // Completion validation
    if (event.status == EventStatus.completed) {
      if (event.completedDate == null) {
        errors.add('Completion date is required for completed events');
      }
      if (event.completedDate != null && event.scheduledDate != null) {
        if (event.completedDate!.isBefore(event.scheduledDate!)) {
          errors.add('Completion date cannot be before scheduled date');
        }
      }
    }

    // Location validation
    if (event.location != null && event.location!.length > 100) {
      errors.add('Location cannot exceed 100 characters');
    }

    // Reminder validation
    if (event.reminderMinutes != null) {
      for (final minutes in event.reminderMinutes!) {
        if (minutes < 0) {
          errors.add('Reminder times cannot be negative');
          break;
        }
        if (minutes > 525600) { // 1 year in minutes
          errors.add('Reminder times cannot exceed 1 year');
          break;
        }
      }
    }

    if (errors.isNotEmpty) {
      throw ValidationException(errors.join('\n'));
    }
  }

  /// Validate event date
  Future<void> _validateEventDate(DateTime scheduledDate, List<String> errors) async {
    final now = DateTime.now();
    final twoYearsFromNow = now.add(const Duration(days: 730));
    final oneYearAgo = now.subtract(const Duration(days: 365));

    // Future date validation
    if (scheduledDate.isAfter(twoYearsFromNow)) {
      errors.add('Scheduled date cannot be more than 2 years in the future');
    }

    // Past date validation (allow up to 1 year ago for historical records)
    if (scheduledDate.isBefore(oneYearAgo)) {
      errors.add('Scheduled date cannot be more than 1 year in the past');
    }
  }

  /// Validate cattle exists
  Future<void> _validateCattleExists(String cattleTagId, List<String> errors) async {
    try {
      final cattle = await _cattleRepository.getCattleByTagId(cattleTagId);
      if (cattle == null) {
        errors.add('Selected cattle does not exist');
      }
    } catch (e) {
      errors.add('Unable to verify cattle existence');
    }
  }

  /// Validate event type exists
  Future<void> _validateEventTypeExists(String eventTypeId, List<String> errors) async {
    try {
      final eventType = await _eventsRepository.getEventTypeByBusinessId(eventTypeId);
      if (eventType == null) {
        errors.add('Selected event type does not exist');
      } else if (eventType.isActive == false) {
        errors.add('Selected event type is inactive');
      }
    } catch (e) {
      errors.add('Unable to verify event type existence');
    }
  }

  // ===== RECURRENCE VALIDATION =====

  /// Validate recurrence settings
  Future<void> validateRecurrence(EventIsar event) async {
    if (event.isRecurring != true) return;

    final errors = <String>[];

    // Recurrence interval validation
    if (event.recurrenceInterval == null || event.recurrenceInterval! <= 0) {
      errors.add('Recurrence interval must be greater than 0');
    }

    if (event.recurrenceInterval != null && event.recurrenceInterval! > 365) {
      errors.add('Recurrence interval cannot exceed 365 days');
    }

    // Recurrence end date validation
    if (event.recurrenceEndDate != null && event.scheduledDate != null) {
      if (event.recurrenceEndDate!.isBefore(event.scheduledDate!)) {
        errors.add('Recurrence end date must be after scheduled date');
      }

      // Maximum recurrence duration (2 years)
      final maxEndDate = event.scheduledDate!.add(const Duration(days: 730));
      if (event.recurrenceEndDate!.isAfter(maxEndDate)) {
        errors.add('Recurrence cannot extend more than 2 years');
      }
    }

    // Maximum recurring events validation
    if (event.recurrenceEndDate != null && 
        event.scheduledDate != null && 
        event.recurrenceInterval != null) {
      final totalEvents = _calculateRecurringEventCount(
        event.scheduledDate!,
        event.recurrenceEndDate!,
        event.recurrencePattern,
        event.recurrenceInterval!,
      );

      if (totalEvents > 100) {
        errors.add('Cannot create more than 100 recurring events');
      }
    }

    // Parent event validation
    if (event.parentEventId?.isNotEmpty ?? false) {
      await _validateParentEventExists(event.parentEventId!, errors);
    }

    if (errors.isNotEmpty) {
      throw ValidationException(errors.join('\n'));
    }
  }

  /// Calculate number of recurring events
  int _calculateRecurringEventCount(
    DateTime startDate,
    DateTime endDate,
    RecurrencePattern pattern,
    int interval,
  ) {
    int count = 0;
    DateTime currentDate = startDate;

    while (currentDate.isBefore(endDate) && count < 100) {
      count++;
      currentDate = _getNextRecurrenceDate(currentDate, pattern, interval);
    }

    return count;
  }

  /// Get next recurrence date
  DateTime _getNextRecurrenceDate(
    DateTime currentDate,
    RecurrencePattern pattern,
    int interval,
  ) {
    switch (pattern) {
      case RecurrencePattern.daily:
        return currentDate.add(Duration(days: interval));
      case RecurrencePattern.weekly:
        return currentDate.add(Duration(days: interval * 7));
      case RecurrencePattern.monthly:
        return DateTime(
          currentDate.year,
          currentDate.month + interval,
          currentDate.day,
          currentDate.hour,
          currentDate.minute,
        );
      case RecurrencePattern.yearly:
        return DateTime(
          currentDate.year + interval,
          currentDate.month,
          currentDate.day,
          currentDate.hour,
          currentDate.minute,
        );
      case RecurrencePattern.custom:
        return currentDate.add(Duration(days: interval));
    }
  }

  /// Validate parent event exists
  Future<void> _validateParentEventExists(String parentEventId, List<String> errors) async {
    try {
      final parentEvent = await _eventsRepository.getEventByBusinessId(parentEventId);
      if (parentEvent == null) {
        errors.add('Parent event does not exist');
      }
    } catch (e) {
      errors.add('Unable to verify parent event existence');
    }
  }

  // ===== EVENT TYPE VALIDATION =====

  /// Validate event type data
  Future<void> validateEventType(EventTypeIsar eventType, {bool isUpdate = false}) async {
    final errors = <String>[];

    // Required field validation
    if (eventType.name?.trim().isEmpty ?? true) {
      errors.add('Event type name is required');
    }

    // Name length validation
    if (eventType.name != null && eventType.name!.length > 50) {
      errors.add('Event type name cannot exceed 50 characters');
    }

    // Description length validation
    if (eventType.description != null && eventType.description!.length > 200) {
      errors.add('Event type description cannot exceed 200 characters');
    }

    // Business ID validation for updates
    if (isUpdate && (eventType.businessId?.trim().isEmpty ?? true)) {
      errors.add('Event type ID is required for updates');
    }

    // Duplicate name validation
    if (eventType.name?.isNotEmpty ?? false) {
      await _validateEventTypeNameUnique(eventType.name!, eventType.businessId, errors);
    }

    // Color validation
    if (eventType.colorHex?.isNotEmpty ?? false) {
      if (!_isValidHexColor(eventType.colorHex!)) {
        errors.add('Invalid color format');
      }
    }

    // Default reminder validation
    if (eventType.defaultReminderMinutes != null) {
      for (final minutes in eventType.defaultReminderMinutes!) {
        if (minutes < 0) {
          errors.add('Default reminder times cannot be negative');
          break;
        }
        if (minutes > 525600) { // 1 year in minutes
          errors.add('Default reminder times cannot exceed 1 year');
          break;
        }
      }
    }

    // Default duration validation
    if (eventType.defaultDurationMinutes != null) {
      if (eventType.defaultDurationMinutes! < 0) {
        errors.add('Default duration cannot be negative');
      }
      if (eventType.defaultDurationMinutes! > 10080) { // 1 week in minutes
        errors.add('Default duration cannot exceed 1 week');
      }
    }

    if (errors.isNotEmpty) {
      throw ValidationException(errors.join('\n'));
    }
  }

  /// Validate event type name is unique
  Future<void> _validateEventTypeNameUnique(
    String name,
    String? currentBusinessId,
    List<String> errors,
  ) async {
    try {
      final allEventTypes = await _eventsRepository.getAllEventTypes();
      final duplicates = allEventTypes.where((et) =>
          et.name?.toLowerCase() == name.toLowerCase() &&
          et.businessId != currentBusinessId);

      if (duplicates.isNotEmpty) {
        errors.add('Event type name already exists');
      }
    } catch (e) {
      errors.add('Unable to verify event type name uniqueness');
    }
  }

  /// Validate hex color format
  bool _isValidHexColor(String color) {
    final hexColorRegex = RegExp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$');
    return hexColorRegex.hasMatch(color);
  }

  // ===== ATTACHMENT VALIDATION =====

  /// Validate event attachment
  Future<void> validateEventAttachment(EventAttachmentIsar attachment) async {
    final errors = <String>[];

    // Required field validation
    if (attachment.eventBusinessId?.trim().isEmpty ?? true) {
      errors.add('Event ID is required for attachments');
    }

    if (attachment.fileName?.trim().isEmpty ?? true) {
      errors.add('File name is required');
    }

    if (attachment.filePath?.trim().isEmpty ?? true) {
      errors.add('File path is required');
    }

    if (attachment.fileType?.trim().isEmpty ?? true) {
      errors.add('File type is required');
    }

    // File name validation
    if (attachment.fileName != null && attachment.fileName!.length > 255) {
      errors.add('File name cannot exceed 255 characters');
    }

    // File size validation
    if (attachment.fileSize == null || attachment.fileSize! <= 0) {
      errors.add('File size must be greater than 0');
    }

    if (attachment.fileSize != null && attachment.fileSize! > 52428800) { // 50MB
      errors.add('File size cannot exceed 50MB');
    }

    // File type validation
    if (attachment.fileType?.isNotEmpty ?? false) {
      await _validateFileType(attachment.fileType!, attachment.fileName, errors);
    }

    // File existence validation
    if (attachment.filePath?.isNotEmpty ?? false) {
      await _validateFileExists(attachment.filePath!, errors);
    }

    // Event existence validation
    if (attachment.eventBusinessId?.isNotEmpty ?? false) {
      await _validateEventExistsForAttachment(attachment.eventBusinessId!, errors);
    }

    // Attachment count validation
    if (attachment.eventBusinessId?.isNotEmpty ?? false) {
      await _validateAttachmentCount(attachment.eventBusinessId!, errors);
    }

    // Image-specific validation
    if (attachment.isImage) {
      _validateImageAttachment(attachment, errors);
    }

    // Document-specific validation
    if (attachment.isDocument) {
      _validateDocumentAttachment(attachment, errors);
    }

    // Video-specific validation
    if (attachment.isVideo) {
      _validateVideoAttachment(attachment, errors);
    }

    if (errors.isNotEmpty) {
      throw ValidationException(errors.join('\n'));
    }
  }

  /// Validate file type
  Future<void> _validateFileType(String fileType, String? fileName, List<String> errors) async {
    final allowedTypes = ['image', 'document', 'video'];
    if (!allowedTypes.contains(fileType.toLowerCase())) {
      errors.add('File type must be image, document, or video');
    }

    // Validate file extension matches type
    if (fileName?.isNotEmpty ?? false) {
      final extension = fileName!.split('.').last.toLowerCase();
      final expectedType = _getFileTypeFromExtension(extension);
      
      if (expectedType != null && expectedType != fileType.toLowerCase()) {
        errors.add('File extension does not match file type');
      }
    }
  }

  /// Get file type from extension
  String? _getFileTypeFromExtension(String extension) {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    const documentExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv'];

    if (imageExtensions.contains(extension)) return 'image';
    if (documentExtensions.contains(extension)) return 'document';
    if (videoExtensions.contains(extension)) return 'video';
    
    return null;
  }

  /// Validate file exists
  Future<void> _validateFileExists(String filePath, List<String> errors) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        errors.add('File does not exist at specified path');
      }
    } catch (e) {
      errors.add('Unable to verify file existence');
    }
  }

  /// Validate event exists for attachment
  Future<void> _validateEventExistsForAttachment(String eventBusinessId, List<String> errors) async {
    try {
      final event = await _eventsRepository.getEventByBusinessId(eventBusinessId);
      if (event == null) {
        errors.add('Event does not exist for attachment');
      }
    } catch (e) {
      errors.add('Unable to verify event existence for attachment');
    }
  }

  /// Validate attachment count limit
  Future<void> _validateAttachmentCount(String eventBusinessId, List<String> errors) async {
    try {
      final attachments = await _eventsRepository.getAttachmentsForEvent(eventBusinessId);
      if (attachments.length >= 10) {
        errors.add('Cannot exceed 10 attachments per event');
      }
    } catch (e) {
      errors.add('Unable to verify attachment count');
    }
  }

  /// Validate image attachment
  void _validateImageAttachment(EventAttachmentIsar attachment, List<String> errors) {
    // Image dimensions validation
    if (attachment.imageWidth != null && attachment.imageWidth! > 4096) {
      errors.add('Image width cannot exceed 4096 pixels');
    }

    if (attachment.imageHeight != null && attachment.imageHeight! > 4096) {
      errors.add('Image height cannot exceed 4096 pixels');
    }

    // Minimum dimensions
    if (attachment.imageWidth != null && attachment.imageWidth! < 1) {
      errors.add('Image width must be at least 1 pixel');
    }

    if (attachment.imageHeight != null && attachment.imageHeight! < 1) {
      errors.add('Image height must be at least 1 pixel');
    }
  }

  /// Validate document attachment
  void _validateDocumentAttachment(EventAttachmentIsar attachment, List<String> errors) {
    // Page count validation
    if (attachment.pageCount != null) {
      if (attachment.pageCount! < 1) {
        errors.add('Document must have at least 1 page');
      }
      if (attachment.pageCount! > 1000) {
        errors.add('Document cannot exceed 1000 pages');
      }
    }
  }

  /// Validate video attachment
  void _validateVideoAttachment(EventAttachmentIsar attachment, List<String> errors) {
    // Duration validation
    if (attachment.durationSeconds != null) {
      if (attachment.durationSeconds! < 1) {
        errors.add('Video duration must be at least 1 second');
      }
      if (attachment.durationSeconds! > 7200) { // 2 hours
        errors.add('Video duration cannot exceed 2 hours');
      }
    }
  }

  // ===== BULK VALIDATION =====

  /// Validate multiple events at once
  Future<Map<String, List<String>>> validateMultipleEvents(List<EventIsar> events) async {
    final results = <String, List<String>>{};

    for (final event in events) {
      try {
        await validateEvent(event);
        results[event.businessId ?? 'unknown'] = [];
      } catch (e) {
        if (e is ValidationException) {
          results[event.businessId ?? 'unknown'] = e.message.split('\n');
        } else {
          results[event.businessId ?? 'unknown'] = ['Validation failed: ${e.toString()}'];
        }
      }
    }

    return results;
  }

  /// Validate event data integrity
  Future<List<String>> validateEventDataIntegrity() async {
    final issues = <String>[];

    try {
      // Check for events with missing cattle
      final events = await _eventsRepository.getAllEvents();
      final cattle = await _cattleRepository.getAllCattle();
      final cattleTagIds = cattle.map((c) => c.tagId).toSet();

      for (final event in events) {
        if (event.cattleTagId?.isNotEmpty ?? false) {
          if (!cattleTagIds.contains(event.cattleTagId)) {
            issues.add('Event "${event.title}" references non-existent cattle: ${event.cattleTagId}');
          }
        }
      }

      // Check for events with missing event types
      final eventTypes = await _eventsRepository.getAllEventTypes();
      final eventTypeIds = eventTypes.map((et) => et.businessId).toSet();

      for (final event in events) {
        if (event.eventTypeId?.isNotEmpty ?? false) {
          if (!eventTypeIds.contains(event.eventTypeId)) {
            issues.add('Event "${event.title}" references non-existent event type: ${event.eventTypeId}');
          }
        }
      }

      // Check for orphaned attachments
      final attachments = await _eventsRepository.getAllEventAttachments();
      final eventIds = events.map((e) => e.businessId).toSet();

      for (final attachment in attachments) {
        if (attachment.eventBusinessId?.isNotEmpty ?? false) {
          if (!eventIds.contains(attachment.eventBusinessId)) {
            issues.add('Attachment "${attachment.fileName}" references non-existent event: ${attachment.eventBusinessId}');
          }
        }
      }

    } catch (e) {
      issues.add('Error validating data integrity: ${e.toString()}');
    }

    return issues;
  }
}